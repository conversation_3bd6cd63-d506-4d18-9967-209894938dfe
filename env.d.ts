/// <reference types="vite/client" />

import PageSection from '@/components/page-section/index.vue';
import Breadcrumb from '@/components/breadcrumb/index.vue';
import GeneralCard from '@/components/general-card/index.vue';
import SearchTable from '@/components/search-table/index.vue';
import RouterButton from '@/components/router-button/index.vue';
import Upload from '@/components/upload/index.vue';
import Image from '@/components/image/index.vue';
import LoadingProvider from '@/components/loading-provider/index.vue';

declare module '*.vue' {
  import { DefineComponent } from 'vue';
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

declare module 'vue' {
  interface GlobalComponents {
    OpenPageSection: typeof PageSection;
    OpenBreadcrumb: typeof Breadcrumb;
    OpenGeneralCard: typeof GeneralCard;
    OpenSearchTable: typeof SearchTable;
    OpenRouterButton: typeof RouterButton;
    OpenUpload: typeof Upload;
    OpenImage: typeof Image;
    OpenLoadingProvider: typeof LoadingProvider;
  }
}
