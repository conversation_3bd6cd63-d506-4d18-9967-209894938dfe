{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "src/**/*.json"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "mitt": ["./node_modules/mitt/index.d.ts"], "@toast-ui/editor": ["./node_modules/@toast-ui/editor/types/index.d.ts"]}}}