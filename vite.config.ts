import { fileURLToPath, URL } from 'node:url';
import { resolve, join } from 'node:path';
import { existsSync } from 'node:fs';
import { homedir } from 'node:os';

import { defineConfig, type UserConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { vitePluginForArco } from '@arco-plugins/vite-vue';
import checker from 'vite-plugin-checker';
import { ViteImageOptimizer } from 'vite-plugin-image-optimizer';
import UnoCSS from 'unocss/vite';
import { visualizer } from 'rollup-plugin-visualizer';

import uploadAlioss from './utils/vite-plugin-upload-alioss';
import replacer from './utils/vite-plugin-replacer';

// 规范化 import 的 path，防止在 windows 下报错
export const normalizePathForImport = (path: string) => {
  return process.platform === 'win32' ? join('file://', path) : path;
}

// https://vitejs.dev/config/
export default defineConfig(async ({ mode }) => {
  let cdnUrlPrefix = 'https://res.oafimg.cn/openatom-admin-test-www/';

  if (mode === 'production') {
    cdnUrlPrefix = 'https://res.oafimg.cn/openatom-admin-www/';
  }

  let currentConfig: UserConfig = {
    base: mode !== 'development' && process.env.USE_CDN === 'yes' ? cdnUrlPrefix : '/',
    plugins: [
      vue({
        template: {
          transformAssetUrls: {
            img: ['src', 'data-src'],
          },
        },
      }),
      vueJsx(),
      UnoCSS(),
      replacer([
        '/@toast-ui/editor/dist/toastui-editor.css',
      ], 'overflow-X: hidden;', 'overflow-x: hidden;'),
      vitePluginForArco({
        modifyVars: {
          hack: `true; @import (reference) "${resolve(
            'src/assets/style/breakpoint.less',
          )}";`,
        },
      }),
      checker({
        eslint: {
          useFlatConfig: true,
          lintCommand: `eslint "src/**/*.{ts,tsx,vue,js}"`,
        },
        stylelint: {
          lintCommand: `stylelint "src/**/*.{scss,css,vue}" --quiet-deprecation-warnings`,
        },
      }),
      ViteImageOptimizer({
        png: {
          quality: 90,
        },
        jpeg: {
          quality: 80,
        },
        jpg: {
          quality: 80,
        },
        cache: true,
        cacheLocation: join(homedir(), '.cache', 'vite-plugin-image-optimizer', 'openatom-admin-www'),
      }),
      process.env.USE_VISUALIZER === 'yes' && visualizer(),
      mode !== 'development' && process.env.USE_CDN === 'yes' && uploadAlioss({
        oss: {
          accessKeyId: 'LTAI5tQkodF222A1aMouKup8',
          accessKeySecret: '******************************',
          endpoint: 'http://oss-cn-beijing.aliyuncs.com',
          bucket: 'openatom',
        },
        urlPrefix: cdnUrlPrefix,
        asset: 'dist',
        removeAfterUpload: true,
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    esbuild: {
      drop: mode !== 'development' ? ['console', 'debugger'] : undefined,
    },
    build: {
      rollupOptions: {
        output: {
          advancedChunks: {
            groups: [
              { name: 'arco', test: /\/node_modules\/@arco-design\/web-vue\// },
              { name: 'chart', test: /\/node_modules\/echarts\// },
              {
                name: 'vue',
                test: /\/node_modules\/(?:vue|vue-router|pinia)\//,
              },
              { name: 'tui-editor', test: /\/node_modules\/@toast-ui\/editor\// },
              {
                name: 'tinymce',
                test: /\/node_modules\/(?:tinymce|tinymce-i18n|@tinymce\/tinymce-vue)\//,
              },
              { name: 'cropperjs', test: /\/node_modules\/cropperjs\// },
              { name: 'date-fns', test: /\/node_modules\/date-fns\// },
              { name: 'marked', test: /\/node_modules\/marked\// },
              {
                name: 'vendors',
                test: /\/node_modules\/(?:lazysizes|mitt|nprogress|pretty-bytes|ts-md5|lodash-es|axios)\//,
              },
            ],
          },
        },
      },
      chunkSizeWarningLimit: 2000,
      reportCompressedSize: false,
      sourcemap: true,
    },
    experimental: {
      renderBuiltUrl(filename: string, { type }: { type: 'public' | 'asset' }) {
        if (type === 'public') {
          return '/' + filename;
        }
        else {
          return (mode !== 'development' && process.env.USE_CDN === 'yes' ? cdnUrlPrefix : '/') + filename;
        }
      },
    },
    server: {
      proxy: {
        '/api': {
          target: 'http://api.oafdev.cn',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api\//, '/'),
        },
      },
    },
  };

  const localViteConfig = fileURLToPath(new URL('./vite.config.local.js', import.meta.url));

  if (existsSync(localViteConfig)) {
    currentConfig = (await import(normalizePathForImport(localViteConfig))).default(currentConfig, { mode });
  }

  return currentConfig;
});
