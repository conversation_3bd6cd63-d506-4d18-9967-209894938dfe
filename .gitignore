# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
#pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
#lib-cov

# Coverage directory used by tools like istanbul
#coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Typescript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
*.local

.nuxt

nuxt-dist/
dist/
/output/

.fuse_hidden*

.stfolder
.stignore

.DS_Store

/utils/*.json
tsconfig.tsbuildinfo
.stylelintcache
vite.config.ts.timestamp-*
vite.config.local.js
/stats.html
