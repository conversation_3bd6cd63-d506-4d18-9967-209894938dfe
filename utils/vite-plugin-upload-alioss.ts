import { normalizePath, type Plugin, type ResolvedConfig, type ResolvedBuildOptions } from 'vite';
import path from 'node:path';
import zlib from 'node:zlib';
import crypto from 'node:crypto';
import fs from 'node:fs';
import { URL } from 'node:url';
import OSS from 'ali-oss';
import mime from 'mime';
import log from 'fancy-log';
import colors from 'ansi-colors';
import { globSync } from 'glob';

export interface OSSOptions {
  filePath: string;
  filename: string;
  ossClient: OSS;
  keyPrefix: string;
  cdnCache: Record<string, boolean>;
}

export interface UploadOptions {
  oss: {
    accessKeyId: string;
    accessKeySecret: string;
    endpoint: string;
    bucket: string;
  };
  urlPrefix: string;
  asset: string;
  removeAfterUpload: boolean;
}

function sha1(content: string, isFull = false) {
  const hash = crypto.createHash('md5').update(content).digest('hex');

  return isFull ? hash : hash.substring(6, 13);
}

async function uploadFile({ filePath, filename, ossClient, keyPrefix, cdnCache }: OSSOptions) {
  let content = fs.readFileSync(filePath);
  const hash = sha1(content.toString(), true);
  const cacheKey = `${filename}?${hash}`;

  if (cdnCache[cacheKey]) {
    return true;
  }

  const ext = path.extname(filePath);
  let contentType: string;

  const charsetMimes: Record<string, string> = {
    '.js': 'utf-8',
    '.css': 'utf-8',
    '.html': 'utf-8',
    '.htm': 'utf-8',
    '.svg': 'utf-8',
  };

  const gzipMimes: Record<string, number> = {
    '.html': 6,
    '.htm': 6,
    '.js': 6,
    '.css': 6,
    '.svg': 6,
  };

  contentType = mime.getType(ext) || 'application/octet-stream';

  if (charsetMimes[ext]) {
    contentType += '; charset=' + charsetMimes[ext];
  }

  const key = `${keyPrefix}${filename}`.replace(/^\/+/, '');

  const headers: Record<string, string> = {
    'Access-Control-Allow-Origin': '*',
    'Content-Type': contentType,
    'Cache-Control': 'max-age=315360000, immutable',
  };

  if (gzipMimes[ext]) {
    headers['Content-Encoding'] = 'gzip';
    content = zlib.gzipSync(content, { level: gzipMimes[ext] }) as Buffer<ArrayBuffer>;
  }

  try {
    await ossClient.put(key, content, { headers });

    cdnCache[cacheKey] = true;
    log('OK:', colors.green(filename + '\tmime: ' + contentType));

    return true;
  }
  catch (error) {
    log('ERR:', colors.red(filename + '\t' + error));

    return false;
  }
}

export default function uploadAlioss(options: UploadOptions): Plugin {
  let cdnCache = {};

  const ossClient = new OSS({
    accessKeyId: options.oss.accessKeyId,
    accessKeySecret: options.oss.accessKeySecret,
    bucket: options.oss.bucket,
    endpoint: options.oss.endpoint,
  });

  const keyPrefix = (new URL(options.urlPrefix)).pathname;
  const asset = options.asset || process.cwd();

  let baseConfig = '/';
  let buildConfig: ResolvedBuildOptions;

  const getCacheFileName = (base: string) => {
    return `__global_system_store__/openatom-admin-www/cdn-manifest.${sha1(JSON.stringify({
      ...options.oss,
      urlPrefix: options.urlPrefix,
      projectBase: base,
    }))}.json`;
  };

  const readCdnCache = async (base: string) => {
    const result = await ossClient.get(getCacheFileName(base));

    return JSON.parse(result.content.toString());
  };

  const writeCdnCache = async (base: string) => {
    await ossClient.put(getCacheFileName(base), Buffer.from(JSON.stringify(cdnCache)), {
      headers: {
        'x-oss-object-acl': 'private',
      },
    });
  };

  return {
    name: 'vite-plugin-upload-alioss',
    enforce: 'post',
    apply: 'build',

    configResolved(config: ResolvedConfig) {
      baseConfig = config.base;
      buildConfig = config.build;
    },

    closeBundle: {
      sequential: true,
      order: 'post',
      async handler() {
        if (!/^http/i.test(baseConfig)) {
          throw Error('[vite-plugin-upload-alioss] base must be a url');
        }

        const outDirPath = normalizePath(path.resolve(normalizePath(buildConfig.outDir)));

        const files = globSync(
          outDirPath + '/**/*',
          {
            nodir: true,
            dot: false,
            ignore: ['**/*.html', '**/favicon*.ico'],
          },
        );

        try {
          cdnCache = await readCdnCache(baseConfig);
        } catch (e) {}

        console.log();
        console.log('============== 开始上传 ==============');
        console.log();

        for (const fileFullPath of files) {
          const cdnName = path.relative(asset, fileFullPath);

          await uploadFile({
            filePath: fileFullPath,
            filename: cdnName,
            ossClient,
            keyPrefix,
            cdnCache,
          });

          if (options.removeAfterUpload) {
            try {
              fs.rmSync(fileFullPath);
            } catch (e) {}
          }
        }

        await writeCdnCache(baseConfig);

        console.log();
        console.log('============== 上传完成 ==============');
        console.log();
      },
    },
  }
}
