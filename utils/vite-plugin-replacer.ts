import { type Plugin } from 'vite';

const escapeRegex = (string: string) => {
  return string.replace(/[/\-\\^$*+?.()|[\]{}]/g, '\\$&');
}

export default function replacer(includes: RegExp[] | string[], searchValue: string | RegExp, replaceValue: string | ((substring: string, ...args: any[]) => string)): Plugin {
  return {
    name: 'vite-plugin-replace',

    transform: (code: string, id: string) => {
      const tests = includes.map(item => {
        if (typeof item === 'string') {
          return new RegExp(`${escapeRegex(item)}(?:$|\\?)`);
        }
        else {
          return item;
        }
      });

      if (tests.find(item => item.test(id))) {
        return {
          code: typeof replaceValue === 'string' ? code.replace(searchValue, replaceValue) : code.replace(searchValue, replaceValue),
          map: null,
        };
      }
    },
  }
}
