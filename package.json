{"name": "openatom-admin-www", "version": "1.0.0", "description": "开放原子开源基金会官网后台前端项目", "repository": {"type": "git", "url": "***************:oadev/openatom-admin-www.git"}, "author": "openatom", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "dev:pm2": "pm2 start pnpm --name openatom-admin-dev -- dev", "build": "run-s type-check \"build-only {@}\" --", "build:stats": "cross-env USE_VISUALIZER=yes run-s type-check build-only", "build:test": "run-s type-check build-only:test", "build:cdn": "cross-env USE_CDN=yes run-s type-check build-only", "build:test:cdn": "cross-env USE_CDN=yes run-s type-check build-only:test", "preview": "vite preview", "build-only": "vite build", "build-only:test": "vite build --mode test", "type-check": "vue-tsc --build", "lint": "run-p lint:eslint lint:stylelint", "lint:eslint": "eslint --cache --max-warnings 0 \"src/**/*.{vue,js,ts,tsx,jsx,cjs,mjs}\"", "lint:stylelint": "stylelint \"src/**/*.{vue,css,less,scss}\" --cache", "code-check": "run-p type-check lint", "prepare": "husky"}, "dependencies": {"@arco-design/web-vue": "~2.56.3", "@tinymce/tinymce-vue": "^6.2.0", "@toast-ui/editor": "^3.2.2", "@vueuse/core": "^13.5.0", "axios": "^1.10.0", "cropperjs": "^1.6.2", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "echarts": "^5.6.0", "lazysizes": "^5.3.2", "lodash-es": "^4.17.21", "marked": "^16.0.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.3", "pretty-bytes": "^7.0.0", "qs": "^6.14.0", "tinymce": "^7.9.1", "tinymce-i18n": "^25.6.2", "ts-md5": "^2.0.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@arco-plugins/vite-vue": "^1.4.5", "@eslint/js": "^9.31.0", "@rushstack/eslint-patch": "^1.12.0", "@stylistic/eslint-plugin": "^5.2.0", "@stylistic/stylelint-config": "^2.0.0", "@stylistic/stylelint-plugin": "^3.1.3", "@tsconfig/node22": "^22.0.2", "@types/ali-oss": "^6.16.11", "@types/fancy-log": "^2.0.2", "@types/lodash-es": "^4.17.12", "@types/node": "^22.16.4", "@types/nprogress": "^0.2.3", "@types/qs": "^6.14.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@unocss/eslint-config": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "ali-oss": "^6.23.0", "ansi-colors": "^4.1.3", "autoprefixer": "^10.4.21", "del": "^8.0.0", "eslint": "^9.31.0", "eslint-plugin-vue": "^10.3.0", "fancy-log": "^2.0.0", "glob": "^11.0.3", "globals": "^16.3.0", "husky": "^9.1.7", "less": "^4.4.0", "meow": "^13.2.0", "mime": "^4.0.7", "npm-run-all2": "^8.0.4", "optionator": "^0.9.4", "postcss": "^8.5.6", "postcss-html": "^1.8.0", "postcss-prefix-selector": "^2.1.1", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.89.2", "sharp": "^0.34.3", "stylelint": "^16.21.1", "stylelint-config-recommended-scss": "^15.0.1", "stylelint-order": "^7.0.0", "svgo": "^4.0.0", "typescript": "~5.8.3", "unocss": "^66.3.3", "vite": "npm:rolldown-vite@^7.0.9", "vite-plugin-checker": "^0.10.0", "vite-plugin-image-optimizer": "^2.0.2", "vue-tsc": "^3.0.1"}, "engines": {"node": ">=22.15.0"}, "browserslist": ["chrome >= 107", "edge >= 107", "firefox >= 104", "safari >= 16"], "pnpm": {"overrides": {"vite": "npm:rolldown-vite@^7.0.5"}, "onlyBuiltDependencies": ["@parcel/watcher", "esbuild", "sharp", "vue-demi"]}}