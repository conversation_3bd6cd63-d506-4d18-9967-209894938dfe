const config = {
  plugins: [
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    require('autoprefixer'),
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    require('postcss-prefix-selector')({
      prefix: 'html[arco-theme=dark]',

      transform: function(prefix, selector, prefixedSelector, filePath) {
        const includeFilePath = [
          '/tinymce/skins/ui/oxide-dark/skin.css',
          '/tinymce/skins/ui/oxide-dark/content.css',
          '/tinymce/skins/content/dark/content.css',
        ];

        if (includeFilePath.find((item) => filePath.indexOf(item) > 0)) {
          return prefixedSelector;
        }

        return selector;
      },
    }),
  ],
};

module.exports = config;
