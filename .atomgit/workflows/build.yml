name: Build

on: push

defaults:
  run:
    shell: bash

jobs:
  build:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
        run-as: root
    steps:
      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      - name: Install pnpm
        run: npm i -g --registry https://registry.npmmirror.com pnpm
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install Dependencies
        run: pnpm install
      - name: Build
        run: pnpm run build
