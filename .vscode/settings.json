{
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "tsconfig.json": "tsconfig.*.json, env.d.ts",
    "vite.config.*": "jsconfig*, vitest.config.*, cypress.config.*, playwright.config.*",
    "package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*, .eslint*, eslint*, .oxlint*, oxlint*, .prettier*, prettier*, .editorconfig, stylelint.config.*, .stylelint*, postcss*, nodemon.json, .npmrc, Dockerfile, .dockerignore"
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "eslint.enable": true,
  "stylelint.enable": true,
  "stylelint.useLocal": true,
  "stylelint.autoFixOnSave": true,
  "typescript.tsdk": "node_modules/typescript/lib",
  "css.validate": false,
  "less.validate": false,
  "scss.validate": false,
  "html.format.enable": false,
  "html.validate.styles": false,
  "html.validate.scripts": false,
  "prettier.enable": false,
  "eslint.useFlatConfig": true,
  "github.copilot.chat.localeOverride": "zh-CN",
  "github.copilot.chat.commitMessageGeneration.instructions": [
    {
      "text": "提交信息必须使用简体中文。",
    },
    {
      "text": "请严格按照 Conventional Commits 格式编写提交信息（commit messages）。使用以下结构：\n\n```\n<type>[可选的 scope]: <gitmoji> <description>\n\n[可选的内容正文]\n```\n\n指南：\n\n1. **Type 和 Scope**: 选择一个合适的 type（例如 `feat`、`fix`），并加上可选的 scope，以描述受影响的模块或功能。\n\n2. **Gitmoji**: 包含一个能够准确体现更改性质的 `gitmoji` 表情符号。\n\n3. **Description**：在标题中写入简洁且具有说明性的描述；如果引用代码或特定术语，请使用反引号（`）包裹。\n\n4. **正文（Body）**: 如需提供更多细节，请使用结构清晰的正文部分：\n   - 使用项目符号（`*`）以提高可读性。\n   - 如适用，清楚地说明更改的动机、背景或技术实现细节。\n\n提交信息应清晰、有内容且专业，有助于提升可读性和项目跟踪效率。"
    }
  ],
}
