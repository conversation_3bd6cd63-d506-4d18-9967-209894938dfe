import { globalIgnores } from 'eslint/config';
import js from '@eslint/js';
import stylistic from '@stylistic/eslint-plugin';
import globals from 'globals';
import pluginVue from 'eslint-plugin-vue';
import { defineConfigWithVueTs, vueTsConfigs } from '@vue/eslint-config-typescript';
import unocss from '@unocss/eslint-config/flat';

export default defineConfigWithVueTs(
  {
    name: 'app/files-to-lint',
    files: ['**/*.{vue,js,ts,tsx}'],
  },

  globalIgnores(['**/dist/**', '**/dist-ssr/**', '**/coverage/**']),

  js.configs.recommended,
  pluginVue.configs['flat/recommended'],
  vueTsConfigs.recommended,
  unocss,
  stylistic.configs['disable-legacy'],

  {
    plugins: {
      '@stylistic': stylistic,
    },
    languageOptions: {
      ecmaVersion: 'latest',
      globals: {
        ...globals.browser,
        ...globals.node,
        NodeListOf: 'readonly',
      },
    },
    rules: {
      '@stylistic/indent': ['warn', 2, { SwitchCase: 1 }],
      '@stylistic/comma-style': [1, 'last'],
      '@stylistic/no-floating-decimal': 2,
      '@stylistic/space-before-function-paren': [1, {
        anonymous: 'never',
        named: 'never',
        asyncArrow: 'always',
      }],
      '@stylistic/keyword-spacing': 2,
      '@stylistic/space-before-blocks': 1,
      '@stylistic/wrap-iife': [2, 'any'],
      '@stylistic/no-extra-semi': 'error',
      '@stylistic/semi': 0,
      '@stylistic/no-mixed-spaces-and-tabs': 1,
      '@stylistic/no-trailing-spaces': 1,
      '@stylistic/space-infix-ops': 1,
      '@stylistic/semi-spacing': 1,
      '@stylistic/key-spacing': [1, { beforeColon: false, afterColon: true, mode: 'strict' }],
      '@stylistic/space-in-parens': [1, 'never'],
      '@stylistic/arrow-spacing': [2, {
        before: true,
        after: true,
      }],
      '@stylistic/block-spacing': [2, 'always'],
      '@stylistic/comma-spacing': [2, {
        before: false,
        after: true,
      }],
      '@stylistic/dot-location': [2, 'property'],
      '@stylistic/eol-last': 2,
      '@stylistic/generator-star-spacing': 0,
      '@stylistic/jsx-quotes': [2, 'prefer-single'],
      '@stylistic/new-parens': 2,
      '@stylistic/no-whitespace-before-property': 2,
      '@stylistic/space-unary-ops': [2, {
        words: true,
        nonwords: false,
      }],
      '@stylistic/template-curly-spacing': [2, 'never'],
      '@stylistic/yield-star-spacing': [2, 'both'],
      '@stylistic/object-curly-spacing': [1, 'always', { objectsInObjects: true }],
      '@stylistic/array-bracket-spacing': [2, 'never'],
      '@stylistic/type-annotation-spacing': 1,
      '@stylistic/quote-props': [1, 'as-needed'],
      '@stylistic/comma-dangle': [1, 'always-multiline'],

      '@typescript-eslint/no-redeclare': 2,
      '@typescript-eslint/no-explicit-any': 0,
      '@typescript-eslint/no-unused-expressions': [2, {
        allowShortCircuit: true,
      }],
      '@typescript-eslint/no-unused-vars': [2, {
        caughtErrors: 'none',
      }],

      'no-console': 'off',
      'default-case': 2,
      curly: [2, 'all'],
      'no-empty': [2, { allowEmptyCatch: true }],
      'no-obj-calls': 2,
      'no-invalid-regexp': 2,
      'no-undef': 2,
      'no-caller': 1,
      'no-unreachable': 2,
      'no-multi-str': 1,
      'no-with': 2,
      'dot-notation': 1,
      'prefer-const': 2,
      'accessor-pairs': 2,
      camelcase: [0, {
        properties: 'always',
      }],
      'constructor-super': 2,
      'handle-callback-err': [2, '^(err|error)$'],
      'no-array-constructor': 2,
      'no-class-assign': 2,
      'no-cond-assign': 2,
      'no-const-assign': 2,
      'no-control-regex': 2,
      'no-delete-var': 2,
      'no-dupe-args': 2,
      'no-dupe-class-members': 2,
      'no-dupe-keys': 2,
      'no-duplicate-case': 2,
      'no-empty-character-class': 2,
      'no-empty-pattern': 2,
      'no-eval': 2,
      'no-ex-assign': 2,
      'no-extra-bind': 2,
      'no-extra-boolean-cast': 2,
      'no-fallthrough': 2,
      'no-func-assign': 2,
      'no-implied-eval': 2,
      'no-inner-declarations': [2, 'functions'],
      'no-irregular-whitespace': 2,
      'no-iterator': 2,
      'no-label-var': 2,
      'no-labels': [2, {
        allowLoop: false,
        allowSwitch: false,
      }],
      'no-lone-blocks': 2,
      'no-native-reassign': 2,
      'no-negated-in-lhs': 2,
      'no-new-object': 2,
      'no-new-require': 2,
      'no-new-symbol': 2,
      'no-new-wrappers': 2,
      'no-octal': 2,
      'no-octal-escape': 2,
      'no-path-concat': 2,
      'no-proto': 2,
      'no-regex-spaces': 2,
      'no-self-assign': 2,
      'no-self-compare': 2,
      'no-sequences': 2,
      'no-shadow-restricted-names': 2,
      'no-spaced-func': 2,
      'no-sparse-arrays': 2,
      'no-this-before-super': 2,
      'no-undef-init': 2,
      'no-unexpected-multiline': 2,
      'no-unneeded-ternary': [2, {
        defaultAssignment: false,
      }],
      'no-unsafe-finally': 2,
      'no-useless-call': 2,
      'no-useless-computed-key': 2,
      'no-useless-constructor': 2,
      'no-useless-escape': 0,
      'use-isnan': 2,
      'valid-typeof': 2,
      yoda: [2, 'never'],
      'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0,
      'no-var': 2,

      'vue/max-attributes-per-line': 0,
      'vue/attributes-order': 0,
      'vue/html-self-closing': ['error', {
        html: {
          void: 'never',
          normal: 'never',
          component: 'always',
        },
        svg: 'always',
        math: 'always',
      }],
      'vue/require-default-prop': 0,
      'vue/no-parsing-error': ['error', {
        'abrupt-closing-of-empty-comment': true,
        'absence-of-digits-in-numeric-character-reference': true,
        'cdata-in-html-content': true,
        'character-reference-outside-unicode-range': true,
        'control-character-in-input-stream': true,
        'control-character-reference': true,
        'eof-before-tag-name': true,
        'eof-in-cdata': true,
        'eof-in-comment': true,
        'eof-in-tag': true,
        'incorrectly-closed-comment': true,
        'incorrectly-opened-comment': true,
        'invalid-first-character-of-tag-name': true,
        'missing-attribute-value': true,
        'missing-end-tag-name': true,
        'missing-semicolon-after-character-reference': true,
        'missing-whitespace-between-attributes': true,
        'nested-comment': true,
        'noncharacter-character-reference': true,
        'noncharacter-in-input-stream': true,
        'null-character-reference': true,
        'surrogate-character-reference': true,
        'surrogate-in-input-stream': true,
        'unexpected-character-in-attribute-name': true,
        'unexpected-character-in-unquoted-attribute-value': true,
        'unexpected-equals-sign-before-attribute-name': true,
        'unexpected-null-character': true,
        'unexpected-question-mark-instead-of-tag-name': true,
        'unexpected-solidus-in-tag': true,
        'unknown-named-character-reference': true,
        'end-tag-with-attributes': true,
        'duplicate-attribute': true,
        'end-tag-with-trailing-solidus': true,
        'non-void-html-element-start-tag-with-trailing-solidus': false,
        'x-invalid-end-tag': true,
        'x-invalid-namespace': true,
      }],
      'vue/multiline-html-element-content-newline': 0,
      'vue/singleline-html-element-content-newline': 0,
      'vue/no-use-v-if-with-v-for': [1, {
        allowUsingIterationVar: true,
      }],
      'vue/require-prop-type-constructor': 0,
      'vue/html-closing-bracket-newline': 0,
      'vue/arrow-spacing': [2, {
        before: true,
        after: true,
      }],
      'vue/block-spacing': [2, 'always'],
      'vue/brace-style': [1, 'stroustrup', { allowSingleLine: true }],
      'vue/key-spacing': [1, { beforeColon: false, afterColon: true, mode: "minimum" }],
      // "vue/keyword-spacing": [2, {"after": true}],
      // 'vue/no-empty-pattern': 2,
      'vue/object-curly-spacing': [1, 'always', { objectsInObjects: true }],
      "vue/space-infix-ops": 1,
      'vue/space-unary-ops': [2, {
        words: true,
        nonwords: false,
      }],
      'vue/custom-event-name-casing': 0,
      'vue/multi-word-component-names': 0,
      'vue/component-name-in-template-casing': [1, 'PascalCase', {
        registeredComponentsOnly: false,
        ignores: [
          '/^[A-Z][a-zA-Z]*(\\.[A-Z][a-zA-Z]*)*$/',
        ],
      }],
      'vue/comma-spacing': [2, {
        before: false,
        after: true,
      }],
      'vue/block-tag-newline': 1,
      'vue/block-lang': ['error',
        {
          script: {
            lang: 'ts',
          },
        },
      ],
      'vue/padding-line-between-blocks': 1,

      'unocss/order-attributify': 0,
    },
  },
);
