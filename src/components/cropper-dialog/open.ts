import type { Emitter, EventType } from 'mitt';

import { createApp } from 'vue';
import mitt from 'mitt';
import CropperDialog from './index.vue';

export default function open(url: string): Promise<string> {
  const emitter: Emitter<Record<EventType, string>> = mitt();

  const app = createApp(CropperDialog, { url, emitter });

  app.mount(document.createElement('div'));

  return new Promise((resolve) => {
    emitter.on('close', () => {
      app.unmount();
      emitter.all.clear();

      resolve(url);
    });

    emitter.on('ok', (processedUrl: string) => {
      url = processedUrl;
    });
  });
}
