// main.vue 改为选项式 API
import { defineAsyncComponent, type ComponentPublicInstance, type InjectionKey } from 'vue';
import type { Emitter, EventType } from 'mitt';
import { createApp } from 'vue';
import MainComponent from './main.vue';
import ErrorComponent from './error.vue';
import globalComponents from '../';

export interface MyComponentPublicInstance extends ComponentPublicInstance {
  open: () => void;
  close: () => void;
  autoAdjustPosition: () => void;
  title: string;
  buttons: [] | object;
  hideClose: boolean;
  disabled: boolean;
  width: number;
  loading: boolean;
  error: string;
  ctx: any;
  emitter: Emitter<Record<EventType, unknown>>;
  $refs: {
    body: {
      dialogClickButton: (button: string) => void,
      dialogOptions: any,
    },
  };
}

export interface AsyncDialogResult {
  button: string;
  data?: any;
}

export interface AsyncDialogUtils {
  setError: () => void;
  autoAdjustPosition: () => void;
  setButton: (button: string, text: string) => void;
  setButtons: (buttons: Record<string, never>) => void;
  setTitle: (text: string) => void;
}

export const AsyncDialogProvide = Symbol() as InjectionKey<AsyncDialogUtils>;

export const dialog = (importFunc: Promise<any>, options: any = {}): Promise<AsyncDialogResult> => {
  return new Promise((resolve) => {
    const boxComponent = {
      name: 'OpenAsyncDialog',
      extends: MainComponent,

      computed: {
        dialogProps() {
          return { ...options.props };
        },
      },

      components: {
        OpenAsyncDialogBody: defineAsyncComponent({
          // 加载函数
          loader: () => importFunc,
          // 加载失败后展示的组件
          errorComponent: ErrorComponent,
        }),
      },
    };

    const app = createApp(boxComponent);
    app.config.errorHandler = (err: any) => {
      if (app._instance) {
        (app._instance as unknown as MyComponentPublicInstance).ctx.setError(err.message);
      }
    }

    app.use(globalComponents);

    const box = app.mount(document.createElement('div')) as MyComponentPublicInstance;

    box.emitter.on('mounted', () => {
      box.loading = false;

      const instance = box.$refs.body;

      if (!instance) {
        return;
      }

      box.autoAdjustPosition();

      box.buttons = options?.buttons ?? instance.dialogOptions?.buttons ?? { ok: '确认', cancel: '取消' };
      box.title = options?.title ?? instance.dialogOptions?.title ?? '对话框';
      box.hideClose = options?.hideClose ?? instance.dialogOptions?.hideClose ?? false;

      if (instance.dialogOptions?.width) {
        box.width = instance.dialogOptions.width;
      }
    });

    box.emitter.on('action', async function(button: any) {
      const instance = box.$refs.body;

      if (box.disabled || !instance) {
        return;
      }

      box.disabled = true;

      if (!instance.dialogClickButton) {
        box.close();

        resolve({ button });

        return;
      }

      const result: any = instance.dialogClickButton(button);

      if (result?.then) {
        result.then((result: any) => {
          if (typeof result === 'object') {
            if (result.close === true || typeof result.close === 'undefined') {
              box.close();

              resolve({ button, data: result });
            }
            else {
              box.disabled = false;
            }
          }
          else {
            if (result === true || typeof result === 'undefined') {
              box.close();

              resolve({ button, data: result });
            }
            else {
              box.disabled = false;
            }
          }
        });
      }
      else {
        if (typeof result === 'object') {
          if (result.close === true || typeof result.close === 'undefined') {
            box.close();

            resolve({ button, data: result });
          }
          else {
            box.disabled = false;
          }
        }
        else {
          if (result === true || typeof result === 'undefined') {
            box.close();

            resolve({ button, data: result });
          }
          else {
            box.disabled = false;
          }
        }
      }
    });

    box.emitter.on('close', () => {
      app.unmount();
    });

    box.title = '正在加载...';
    box.width = options.width;
    box.open();
  });
}
