import type { Editor, Ui } from 'tinymce';

import tinymce from 'tinymce';
import openCropperDialog from '@/components/cropper-dialog/open';

const isNullable = (a: any) => a === null || a === undefined;
const isNonNullable = (a: any) => !isNullable(a);
const isPlaceholderImage = (imgElm: Element) => imgElm.nodeName === 'IMG' && (imgElm.hasAttribute('data-mce-object') || imgElm.hasAttribute('data-mce-placeholder'));
const isFigure = (elm: Element) => isNonNullable(elm) && elm.nodeName === 'FIGURE';
const isImage = (elm: Element) => elm.nodeName === 'IMG';

const getSelectedImage = (editor: Editor) => {
  const imgElm = editor.selection.getNode();
  const figureElm = editor.dom.getParent(imgElm, 'figure.image');
  if (figureElm) {
    return editor.dom.select('img', figureElm)[0];
  }
  if (imgElm && (imgElm.nodeName !== 'IMG' || isPlaceholderImage(imgElm))) {
    return null;
  }
  return imgElm;
};

const openDialog = async (editor: Editor) => {
  const image = getSelectedImage(editor) as HTMLImageElement;

  const url = await openCropperDialog(image.src);

  editor.execCommand('mceUpdateImage', false, { src: url, width: '', height: '' });
};

const toggleState = (editor: Editor, toggler: () => void) => {
  editor.on('NodeChange', toggler);
  return () => editor.off('NodeChange', toggler);
};

const toggleCropperState = (editor: Editor) => (api: Ui.Toolbar.ToolbarToggleButtonInstanceApi | Ui.Menu.MenuItemInstanceApi) => {
  const updateEnabled = () => {
    api.setEnabled(isNonNullable(getSelectedImage(editor)) && editor.selection.isEditable());
  };
  updateEnabled();
  return toggleState(editor, () => updateEnabled());
};

tinymce.PluginManager.add('cropper', (editor) => {
  editor.ui.registry.addButton('cropper', {
    icon: 'crop',
    tooltip: '裁剪图片',
    onAction: () => {
      openDialog(editor);
    },
    onSetup: toggleCropperState(editor),
  });

  editor.ui.registry.addMenuItem('cropper', {
    icon: 'crop',
    text: '裁剪图片...',
    onAction: () => {
      openDialog(editor);
    },
    onSetup: toggleCropperState(editor),
  });

  editor.ui.registry.addContextMenu('cropper', {
    update: (element: Element) => editor.selection.isEditable() && (isFigure(element) || isImage(element) && !isPlaceholderImage(element)) ? ['cropper'] : [],
  });

  return {};
});
