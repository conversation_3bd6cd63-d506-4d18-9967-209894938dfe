import tinymce from 'tinymce';
import { marked } from 'marked';
import { seq, replaceAsync } from './replace';
import { uploadImage } from './upload';

const reImage = /!\[([^\]]*?)\]\(([^)]*?)\)/g;

const uploadImageAndReplaceUrl = async (content: string) => await replaceAsync(content, reImage, seq(async (_: string, p1: string, p2: string) => {
  const url = await uploadImage(p2);

  return `![${p1}](${url})`;
}));

tinymce.PluginManager.add('markdownit', (editor) => {
  const openDialog = () => editor.windowManager.open({
    title: '插入 Markdown 内容',
    size: 'large',
    body: {
      type: 'panel',
      items: [{
        type: 'textarea',
        name: 'code',
      }],
    },
    buttons: [
      {
        type: 'cancel',
        name: 'cancel',
        text: 'Cancel',
      },
      {
        type: 'submit',
        name: 'save',
        text: 'Save',
        primary: true,
      },
    ],
    async onSubmit(api) {
      let content = api.getData().code.trim();

      if (content) {
        api.block('请稍候...');

        content = await uploadImageAndReplaceUrl(content);

        editor.insertContent(marked.parse(content) as string);
      }

      api.close();
    },
  });

  /* Add a button that opens a window */
  editor.ui.registry.addButton('markdownit', {
    text: 'MD',
    icon: 'sourcecode',
    tooltip: '插入 Markdown 内容',
    onAction: () => {
      openDialog();
    },
  });

  /* Adds a menu item, which can then be included in any menu via the menu/menubar configuration */
  editor.ui.registry.addMenuItem('markdownit', {
    icon: 'sourcecode',
    text: '插入 Markdown',
    onAction: () => {
      openDialog();
    },
  });

  return {};
});
