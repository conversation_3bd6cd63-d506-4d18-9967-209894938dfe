<script lang="ts" setup>
import { inject, type PropType } from 'vue';
import { type RouteRecordRaw } from 'vue-router';

defineOptions({
  name: 'OpenMenuItem',
});

const props = defineProps({
  elements: {
    type: Array as PropType<RouteRecordRaw[]>,
    default: () => [],
  },
});

const goto = inject('menuGoto') as (item: RouteRecordRaw) => void;
</script>

<template>
  <template v-for="element in props.elements" :key="element.meta?.__fullPath">
    <template v-if="element.children?.length !== 0">
      <ASubMenu class="open-menu-item" :title="element.meta?.title" :key="element.meta?.__fullPath">
        <template #icon v-if="element.meta?.icon">
          <Component :is="element.meta.icon" />
        </template>
        <OpenMenuItem :elements="element.children" />
      </ASubMenu>
    </template>
    <template v-else>
      <AMenuItem class="open-menu-item" :key="element.meta?.__fullPath" @click="goto(element)">
        <template #icon v-if="element.meta?.icon">
          <Component :is="element.meta.icon" />
        </template>
        <span>{{ element.meta?.title }}</span>
      </AMenuItem>
    </template>
  </template>
</template>

<style lang="scss" scoped>
</style>
