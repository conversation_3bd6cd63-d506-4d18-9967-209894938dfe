import { computed } from 'vue';
import { type RouteRecordRaw, type RouteRecordNormalized } from 'vue-router';
import usePermission from '@/hooks/permission';
import appClientMenus from '@/router/app-menus';
import { cloneDeep } from 'lodash-es';

const removeOptionalParameter = (path: string) => {
  return path.replace(/:[a-zA-Z0-9_]+[?*]/g, '').replace(/\/\//g, '/').replace(/\/$/g, '');
};

export default function useMenuTree() {
  const permission = usePermission();

  const menuTree = computed(() => {
    const copyRouter = cloneDeep(appClientMenus) as RouteRecordNormalized[];

    copyRouter.sort((a: RouteRecordNormalized, b: RouteRecordNormalized) => {
      return (a.meta.order || 0) - (b.meta.order || 0);
    });

    function travel(_routes: RouteRecordRaw[], layer: number, parentPath: string) {
      if (!_routes) {
        return null;
      }

      const collector: any = _routes.map((element) => {
        // no access
        if (!permission.accessRouter(element) || element.meta?.hideInMenu === true) {
          return null;
        }

        element.path = removeOptionalParameter(element.path);

        const fullPath = `${parentPath}${element.path.startsWith('/') ? '' : '/'}${element.path}`;

        if (element.meta) {
          element.meta.__fullPath = fullPath;
        }

        // leaf node
        if (element.meta?.hideChildrenInMenu || !element.children) {
          element.children = [];
          return element;
        }

        // route filter hideInMenu true
        element.children = element.children.filter(
          (x) => x.meta?.hideInMenu !== true,
        );

        // Associated child node
        const subItem = travel(element.children, layer + 1, fullPath);

        if (subItem.length) {
          element.children = subItem;
          return element;
        }
        // the else logic
        if (layer > 1) {
          element.children = subItem;
          return element;
        }

        if (element.meta?.hideInMenu === false) {
          return element;
        }

        return null;
      });

      return collector.filter(Boolean);
    }

    return travel(copyRouter, 0, '');
  });

  return {
    menuTree,
  };
}
