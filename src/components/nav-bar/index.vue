<script lang="ts" setup>
import { computed, inject, ref } from 'vue';
import { useDark, useToggle, useFullscreen } from '@vueuse/core';
import { useAppStore, useUserStore } from '@/store';
import useUser from '@/hooks/user';

defineOptions({
  name: 'OpenNavBar',
});

const appStore = useAppStore();
const userStore = useUserStore();
const { logout } = useUser();
const { isFullscreen, toggle: toggleFullScreen } = useFullscreen();
const theme = computed(() => {
  return appStore.theme;
});
const isDark = useDark({
  selector: 'body',
  attribute: 'arco-theme',
  valueDark: 'dark',
  valueLight: 'light',
  storageKey: 'arco-theme',
  onChanged(dark: boolean) {
    // overridden default behavior
    appStore.toggleTheme(dark);
  },
});
const toggleTheme = useToggle(isDark);
const handleToggleTheme = () => {
  toggleTheme();
};
const handleLogout = () => {
  logout();
};
const toggleDrawerMenu = inject('toggleDrawerMenu') as () => void;

const navBarEl = ref();

const getEnvTitle = () => {
  const mode = import.meta.env.MODE;

  if (mode === 'test') {
    return '【测试环境】';
  }
  else if (mode === 'development') {
    return '【开发环境】';
  }
  else {
    return '';
  }
}
</script>

<template>
  <div class="open-nav-bar" ref="navBarEl">
    <div class="left-side">
      <ASpace>
        <RouterLink to="/" custom v-slot="{ navigate }">
          <img alt="logo" src="@/assets/images/logo.svg" class="h-35px cursor-pointer" @click="navigate">
        </RouterLink>
        <RouterLink to="/" custom v-slot="{ navigate }">
          <ATypographyTitle class="cursor-pointer m-0! text-18px! line-height-none!" :heading="1" @click="navigate">
            开放原子开源基金会{{ getEnvTitle() }}
          </ATypographyTitle>
        </RouterLink>
        <IconMenuFold v-if="appStore.device === 'mobile'" class="cursor-pointer text-22px" @click="toggleDrawerMenu" />
      </ASpace>
    </div>
    <ul class="right-side">
      <li>
        <ATooltip :content=" theme === 'light' ? '点击切换为暗黑模式' : '点击切换为亮色模式' ">
          <AButton class="nav-btn" type="outline" :shape="'circle'" @click="handleToggleTheme">
            <template #icon>
              <IconMoonFill v-if="theme === 'dark'" />
              <IconSunFill v-else />
            </template>
          </AButton>
        </ATooltip>
      </li>
      <li>
        <ATooltip :content=" isFullscreen ? '点击退出全屏模式' : '点击切换全屏模式' ">
          <AButton class="nav-btn" type="outline" :shape="'circle'" @click="toggleFullScreen">
            <template #icon>
              <IconFullscreenExit v-if="isFullscreen" />
              <IconFullscreen v-else />
            </template>
          </AButton>
        </ATooltip>
      </li>
      <li v-if="userStore.user.name">
        <ADropdown trigger="click" :popup-container="navBarEl">
          <div class="h-full w-full flex cursor-pointer items-center text-[color:var(--color-text-1)]">
            <AAvatar :size="32" class="mr-8px cursor-pointer bg-[color:var(--color-fill-1)]!">
              <img :src="userStore.user.photo" :alt="userStore.user.name" class="object-cover">
            </AAvatar>
            {{ userStore.user.name }}
          </div>
          <template #content>
            <ADoption>
              <ASpace @click="handleLogout">
                <IconExport />
                <span>退出登录</span>
              </ASpace>
            </ADoption>
          </template>
        </ADropdown>
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped>
.open-nav-bar {
  display: flex;
  justify-content: space-between;
  height: 100%;
  background-color: var(--color-bg-2);
  border-bottom: 1px solid var(--color-border);

  .left-side {
    display: flex;
    align-items: center;
    padding-left: 20px;
  }

  .right-side {
    display: flex;
    padding-right: 20px;
    list-style: none;

    :deep(.locale-select) {
      border-radius: 20px;
    }

    li {
      display: flex;
      align-items: center;
      padding: 0 10px;
    }

    a {
      color: var(--color-text-1);
      text-decoration: none;
    }

    .nav-btn {
      color: rgb(var(--gray-8));
      font-size: 16px;
      border-color: rgb(var(--gray-2));
    }

    .trigger-btn,
    .ref-btn {
      position: absolute;
      bottom: 14px;
    }

    .trigger-btn {
      margin-left: 14px;
    }
  }
}
</style>
