<script lang="ts" setup>
import { ref, onUnmounted } from 'vue';
import type { RouteLocationNormalized } from 'vue-router';
import {
  listenerRouteChange,
  removeRouteListener,
} from '@/utils/route-listener';
import { useTabBarStore } from '@/store';
import OpenTabBarItem from './item.vue';

defineOptions({
  name: 'OpenTabBar',
});

const tabBarStore = useTabBarStore();
const affixRef = ref();

listenerRouteChange((route: RouteLocationNormalized) => {
  if (
    !route.meta.noAffix &&
    !tabBarStore.tagList.some(tag => tag.name === route.path)
  ) {
    tabBarStore.updateTabList(route);
  }
}, true);

onUnmounted(() => {
  removeRouteListener();
});

const tabBarEl = ref();
</script>

<template>
  <div class="open-tab-bar">
    <AAffix ref="affixRef" :offset-top="60">
      <div class="tab-bar-box" ref="tabBarEl">
        <div class="tab-bar-scroll">
          <div class="tags-wrap">
            <OpenTabBarItem
              v-for="(tag, index) in tabBarStore.tagList"
              :key="tag.name"
              :index="index"
              :item-data="tag"
              :container="tabBarEl"
            />
          </div>
        </div>
      </div>
    </AAffix>
  </div>
</template>

<style lang="scss" scoped>
.open-tab-bar {
  position: relative;
  background-color: var(--color-bg-2);

  &:deep() {
    .arco-affix {
      z-index: 98;
    }
  }

  .tab-bar-box {
    display: flex;
    padding: 0 20px;
    background-color: var(--color-bg-2);
    border-bottom: 1px solid var(--color-border);

    .tab-bar-scroll {
      flex: 1;
      overflow: hidden;

      .tags-wrap {
        padding-top: 4px;

        :deep(.arco-tag) {
          display: inline-flex;
          align-items: center;
          margin-right: 6px;
          margin-bottom: 4px;
          cursor: pointer;

          &:first-child {
            .arco-tag-close-btn {
              display: none;
            }
          }
        }
      }
    }
  }
}
</style>
