<script lang="ts" setup>
import { type PropType, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useTabBarStore } from '@/store';
import type { TagProps } from '@/store/modules/tab-bar/types';
import { DEFAULT_ROUTE_PATH, REDIRECT_ROUTE_PATH } from '@/router/constants';

enum Eaction {
  reload = 'reload',
  current = 'current',
  left = 'left',
  right = 'right',
  others = 'others',
  all = 'all',
}

defineOptions({
  name: 'OpenTabBarItem',
});

const props = defineProps({
  itemData: {
    type: Object as PropType<TagProps>,
    default: () => ({}),
  },
  index: {
    type: Number,
    default: 0,
  },
  container: {
    type: Object as PropType<HTMLElement>,
    default: null,
  },
});

const router = useRouter();
const route = useRoute();
const tabBarStore = useTabBarStore();

const goto = (tag: TagProps) => {
  router.push({ path: tag.name });
};
const tagList = computed(() => {
  return tabBarStore.tagList;
});

const disabledReload = computed(() => {
  return props.itemData.name !== route.path;
});

const disabledCurrent = computed(() => {
  return props.index === 0;
});

const disabledLeft = computed(() => {
  return [0, 1].includes(props.index);
});

const disabledRight = computed(() => {
  return props.index === tagList.value.length - 1;
});

const tagClose = (tag: TagProps, idx: number) => {
  tabBarStore.deleteTag(idx, tag);
  if (props.itemData.name === route.path) {
    const latest = tagList.value[idx - 1]; // 获取队列的前一个tab
    router.push({ path: latest.name });
  }
};

const findCurrentRouteIndex = () => {
  return tagList.value.findIndex(el => el.name === route.path);
};

const actionSelect = async (value: any) => {
  const { itemData, index } = props;
  const copyTagList = [...tagList.value];

  if (value === Eaction.current) {
    tagClose(itemData, index);
  }
  else if (value === Eaction.left) {
    const currentRouteIdx = findCurrentRouteIndex();
    copyTagList.splice(1, props.index - 1);

    tabBarStore.freshTabList(copyTagList);
    if (currentRouteIdx < index) {
      router.push({ path: itemData.name });
    }
  }
  else if (value === Eaction.right) {
    const currentRouteIdx = findCurrentRouteIndex();
    copyTagList.splice(props.index + 1);

    tabBarStore.freshTabList(copyTagList);
    if (currentRouteIdx > index) {
      router.push({ path: itemData.name });
    }
  }
  else if (value === Eaction.others) {
    const filterList = tagList.value.filter((el, idx) => {
      return idx === 0 || idx === props.index;
    });
    tabBarStore.freshTabList(filterList);
    router.push({ path: itemData.name });
  }
  else if (value === Eaction.reload) {
    tabBarStore.deleteCache(itemData);
    await router.push({
      path: REDIRECT_ROUTE_PATH,
      query: {
        path: route.fullPath,
      },
    });
    tabBarStore.addCache(itemData.name);
  }
  else {
    tabBarStore.resetTabList();
    router.push({ path: DEFAULT_ROUTE_PATH });
  }
};
</script>

<template>
  <ADropdown
    class="open-tab-bar-item"
    trigger="contextMenu"
    :popup-max-height="false"
    @select="actionSelect"
    position="bl"
    :popup-container="props.container"
  >
    <span
      class="arco-tag arco-tag-size-medium arco-tag-checked"
      :class="{ 'link-activated': itemData.name === route.path }"
      @click="goto(itemData)"
    >
      <span class="tag-link">
        {{ itemData.title }}
      </span>
      <span
        class="arco-icon-hover arco-tag-icon-hover arco-icon-hover-size-medium arco-tag-close-btn"
        @click.stop="tagClose(itemData, index)"
      >
        <IconClose />
      </span>
    </span>
    <template #content>
      <ADoption :disabled="disabledReload" :value="Eaction.reload">
        <IconRefresh />
        <span>重新加载</span>
      </ADoption>
      <ADoption
        class="sperate-line"
        :disabled="disabledCurrent"
        :value="Eaction.current"
      >
        <IconClose />
        <span>关闭当前标签页</span>
      </ADoption>
      <ADoption :disabled="disabledLeft" :value="Eaction.left">
        <IconToLeft />
        <span>关闭左侧标签页</span>
      </ADoption>
      <ADoption
        class="sperate-line"
        :disabled="disabledRight"
        :value="Eaction.right"
      >
        <IconToRight />
        <span>关闭右侧标签页</span>
      </ADoption>
      <ADoption :value="Eaction.others">
        <IconSwap />
        <span>关闭其它标签页</span>
      </ADoption>
      <ADoption :value="Eaction.all">
        <IconFolderDelete />
        <span>关闭全部标签页</span>
      </ADoption>
    </template>
  </ADropdown>
</template>

<style lang="scss" scoped>
.tag-link {
  color: var(--color-text-2);
  text-decoration: none;
}

.link-activated {
  color: rgb(var(--link-6));

  .tag-link {
    color: rgb(var(--link-6));
  }

  & + .arco-tag-close-btn {
    color: rgb(var(--link-6));
  }
}

:deep(.arco-dropdown-option-content) {
  span {
    margin-left: 10px;
  }
}

.arco-dropdown-open {
  .tag-link {
    color: rgb(var(--danger-6));
  }

  .arco-tag-close-btn {
    color: rgb(var(--danger-6));
  }
}

.sperate-line {
  border-bottom: 1px solid var(--color-neutral-3);
}
</style>
