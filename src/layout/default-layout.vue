<script lang="ts" setup>
import { ref, computed, watch, provide, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAppStore, useUserStore } from '@/store';
import OpenNavBar from '@/components/nav-bar/index.vue';
import OpenMenu from '@/components/menu/index.vue';
import OpenTabBar from '@/components/tab-bar/index.vue';
import usePermission from '@/hooks/permission';
import useResponsive from '@/hooks/responsive';
import OpenPageLayout from './page-layout.vue';

defineOptions({
  name: 'OpenDefaultLayout',
});

const isInit = ref(false);
const appStore = useAppStore();
const userStore = useUserStore();
const router = useRouter();
const route = useRoute();
const permission = usePermission();
useResponsive(true);
const hideMenu = computed(() => appStore.hideMenu);
const menuWidth = computed(() => appStore.menuCollapse ? 48 : 220);
const collapsed = computed(() => appStore.menuCollapse);
const paddingStyle = computed(() => {
  const paddingLeft = !hideMenu.value ? { paddingLeft: `${menuWidth.value}px` } : {};
  const paddingTop = { paddingTop: '60px' };
  return { ...paddingLeft, ...paddingTop };
});
const hideBreadcrumb = computed(() => route.path === '/welcome/index');
const setCollapsed = (val: boolean) => {
  // for page initialization menu state problem
  if (!isInit.value) {
    return;
  }
  appStore.menuCollapse = val;
};
watch(
  () => userStore.user.roles,
  (roleValue) => {
    if (roleValue && !permission.accessRouter(route)) {
      router.push({ name: 'notFound' });
    }
  },
);
const drawerVisible = ref(false);
const drawerCancel = () => {
  drawerVisible.value = false;
};
provide('toggleDrawerMenu', () => {
  drawerVisible.value = !drawerVisible.value;
});
onMounted(() => {
  isInit.value = true;
});

const listing = computed(() => {
  let result: (string | undefined)[] = [];

  if (!route.meta.hideChildrenInMenu && route.matched.length > 1 && route.meta.__breadcrumb) {
    result = route.meta.__breadcrumb.map(item => item.title);
  }

  result.push(route.meta.title);

  return result;
});
</script>

<template>
  <ALayout class="layout" :class="{ mobile: appStore.hideMenu }">
    <div class="layout-nav-bar">
      <OpenNavBar />
    </div>
    <ALayout>
      <ALayoutSider
        v-show="!hideMenu"
        class="layout-sider"
        breakpoint="xl"
        :collapsed="collapsed"
        collapsible
        :width="menuWidth"
        :style="{ paddingTop: '60px' }"
        hide-trigger
        @collapse="setCollapsed"
      >
        <div class="menu-wrapper">
          <OpenMenu />
        </div>
      </ALayoutSider>
      <ADrawer
        v-if="hideMenu"
        :visible="drawerVisible"
        placement="left"
        :footer="false"
        mask-closable
        :closable="false"
        @cancel="drawerCancel"
      >
        <OpenMenu />
      </ADrawer>
      <ALayout class="layout-content" :style="paddingStyle">
        <OpenTabBar />

        <ALayoutContent>
          <OpenBreadcrumb :items="listing as string[]" class="my-16px ml-20px" v-if="!hideBreadcrumb" />

          <OpenPageLayout />
        </ALayoutContent>
      </ALayout>
    </ALayout>
  </ALayout>
</template>

<style lang="scss" scoped>
$nav-size-height: 60px;
$layout-max-width: 1100px;

.layout {
  width: 100%;
  height: 100%;
}

.layout-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  height: $nav-size-height;
}

.layout-sider {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  height: 100%;
  transition: all 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);

  &::after {
    position: absolute;
    top: 0;
    right: -1px;
    display: block;
    width: 1px;
    height: 100%;
    background-color: var(--color-border);
    content: "";
  }

  > :deep(.arco-layout-sider-children) {
    overflow-y: hidden;
  }
}

.menu-wrapper {
  height: 100%;
  overflow: auto;
  overflow-x: hidden;

  :deep(.arco-menu) {
    ::-webkit-scrollbar {
      width: 12px;
      height: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background-color: var(--color-text-4);
      background-clip: padding-box;
      border: 4px solid transparent;
      border-radius: 7px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background-color: var(--color-text-3);
    }
  }
}

.layout-content {
  min-height: 100vh;
  overflow-y: hidden;
  background-color: var(--color-fill-2);
  transition: padding 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);

  :deep(.arco-form) {
    &.arco-form-layout-horizontal {
      .arco-form-item {
        .arco-picker {
          width: 100%;
        }
      }
    }
  }
}
</style>
