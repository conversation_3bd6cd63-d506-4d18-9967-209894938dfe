<script lang="ts" setup>
defineOptions({
  name: 'OpenPageLayout',
});
</script>

<template>
  <RouterView v-slot="{ Component, route }">
    <Component :is="Component" :key="route.path" v-if="route.meta.ignoreCache" />
    <KeepAlive max="10">
      <Component :is="Component" :key="route.path" v-if="!route.meta.ignoreCache" />
    </KeepAlive>
  </RouterView>
</template>

<style lang="scss" scoped>
</style>
