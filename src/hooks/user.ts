import { useRouter } from 'vue-router';
import NProgress from 'nprogress';
import { useUserStore } from '@/store';
import { gotoLogin } from '@/utils/auth';

export default function useUser() {
  const router = useRouter();
  const userStore = useUserStore();

  const logout = async (logoutTo?: string) => {
    NProgress.start();
    await userStore.logout();
    NProgress.done();

    const currentRoute = router.currentRoute.value;

    gotoLogin(logoutTo && typeof logoutTo === 'string' ? logoutTo : currentRoute.path);
  };

  return {
    logout,
  };
}
