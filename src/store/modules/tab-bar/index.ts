import type { RouteLocationNormalized } from 'vue-router';
import { defineStore } from 'pinia';
import {
  DEFAULT_ROUTE_TITLE,
  DEFAULT_ROUTE_PATH,
  REDIRECT_ROUTE_PATH,
} from '@/router/constants';
import { isString } from '@/utils/is';
import { type TagProps } from './types';
import { ref } from 'vue';

const defaultTag = {
  title: DEFAULT_ROUTE_TITLE,
  name: DEFAULT_ROUTE_PATH,
};

const formatTag = (route: RouteLocationNormalized): TagProps => {
  const { meta, path, query, matched } = route;

  let titles = [meta.title];

  if (matched.length > 1 && meta.__breadcrumb) {
    titles = titles.concat(meta.__breadcrumb.map(item => item.title).reverse());
  }

  return {
    title: titles.join(' - '),
    name: path,
    query,
    ignoreCache: meta.ignoreCache,
  };
};

const BAN_LIST = [REDIRECT_ROUTE_PATH];

const useTabBarStore = defineStore('tabBar', () => {
  const tagList = ref<TagProps[]>([defaultTag]);
  const cacheTabList = new Set([DEFAULT_ROUTE_PATH]);

  const updateTabList = (route: RouteLocationNormalized) => {
    if (BAN_LIST.includes(route.path as string)) {
      return;
    }
    tagList.value.push(formatTag(route));
    if (!route.meta.ignoreCache) {
      cacheTabList.add(route.path as string);
    }
  }

  const deleteTag = (idx: number, tag: TagProps) => {
    tagList.value.splice(idx, 1);
    cacheTabList.delete(tag.name);
  }

  const addCache = (name: string) => {
    if (isString(name) && name !== '') {
      cacheTabList.add(name);
    }
  }

  const deleteCache = (tag: TagProps) => {
    cacheTabList.delete(tag.name);
  }

  const freshTabList = (tags: TagProps[]) => {
    tagList.value = tags;
    cacheTabList.clear();
    // 要先判断ignoreCache
    tagList.value
      .filter((el) => !el.ignoreCache)
      .map((el) => el.name)
      .forEach((x) => cacheTabList.add(x));
  }

  const resetTabList = () => {
    tagList.value = [defaultTag];
    cacheTabList.clear();
    cacheTabList.add(DEFAULT_ROUTE_PATH);
  }

  return { tagList, updateTabList, deleteTag, addCache, deleteCache, freshTabList, resetTabList };
});

export default useTabBarStore;
