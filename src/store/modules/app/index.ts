import { defineStore } from 'pinia';
import { ref } from 'vue';

const useAppStore = defineStore('app', () => {
  const theme = ref('light');
  const hideMenu = ref(false);
  const menuCollapse = ref(false);
  const device = ref('desktop');

  // Change theme color
  const toggleTheme = (dark: boolean) => {
    if (dark) {
      theme.value = 'dark';
      document.querySelector('html')?.setAttribute('arco-theme', 'dark');
      document.querySelector('body')?.setAttribute('arco-theme', 'dark');
    }
    else {
      theme.value = 'light';
      document.querySelector('html')?.removeAttribute('arco-theme');
      document.querySelector('body')?.removeAttribute('arco-theme');
    }
  }

  const toggleDevice = (newDevice: string) => {
    device.value = newDevice;
  }

  const toggleMenu = (value: boolean) => {
    hideMenu.value = value;
  }

  return { theme, hideMenu, menuCollapse, device, toggleTheme, toggleDevice, toggleMenu };
});

export default useAppStore;
