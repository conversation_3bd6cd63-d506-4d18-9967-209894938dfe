<script lang="ts" setup>
import { ref, watch } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { useLanguage, onLanguageRouteUpdate } from '@/hooks/i18n';
import MarkdownEditor from '@/components/markdown-editor/index.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },

  type: {
    type: String,
    default: 'add',
  },
});

const router = useRouter();
const { success } = useNotification();
const language = useLanguage();

const loading = ref(false);
const formData = ref<Record<string, any>>({
  title: '',
  realm: props.id,
  category: '',
  orderNum: 1,
  cover: '',
  content: '',
  url: '',
});
const listing1 = ref<any[]>([]);
const listing2 = ref<any[]>([]);
const formRef = ref<FormInstance>();
const resourceConfig = ref<string[]>([]);
let dataLanguage = language;

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.type === 'edit' && (dataLanguage === language) ? '/admin/v1/law/resource/update' : '/admin/v1/law/resource/create', {
    data: {
      resourceId: props.type === 'edit' ? props.id : '',
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/law/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/law/resource/detail', {
    data: {
      resourceId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    dataLanguage = data.lc;

    formData.value = {
      title: data.title,
      realm: data.realm,
      category: data.category,
      cover: data.cover,
      orderNum: data.orderNum,
      content: data.content,
      url: data.url,
    };
  }
};

const getList1 = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/law/resource/category/list');

  if (isSuccess) {
    listing1.value = data.list;
  }
}

const getList2 = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/law/resource/category/list', {
    data: {
      parentId: formData.value.realm,
    },
  });

  if (isSuccess) {
    listing2.value = data.list;
  }
}

const setResourceConfig = () => {
  resourceConfig.value = JSON.parse(listing2.value.find((item) => item.categoryId === formData.value.category)?.resourceConfig || '[]');
}

const init = async () => {
  if (props.type === 'edit') {
    await getDetail();
  }

  if (props.id) {
    await getList2();
    setResourceConfig();
  }

  await getList1();
}

const changeList1 = () => {
  formData.value.category = '';
  getList2();
}

watch(() => formData.value.category, () => {
  setResourceConfig();
});

onLanguageRouteUpdate(() => {
  init();
});

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard :show-i18n="props.type === 'edit'">
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" v-confirm>
        <AFormItem v-if="props.type !== 'edit'">
          <AAlert>请不要忘记输入英文内容！新建后，编辑这条信息，然后点击右上角【切换到英文内容】来输入英文内容！</AAlert>
        </AFormItem>

        <AFormItem label="一级分类" field="realm" :rules="[{ required: true, message: '请选择一级分类' }]">
          <ASelect v-model="formData.realm" :fallback-option="false" placeholder="请选择一级分类" @change="changeList1">
            <AOption v-for="item in listing1" :key="item.categoryId" :value="item.categoryId">{{ item.categoryName }}</AOption>
          </ASelect>
        </AFormItem>

        <AFormItem label="二级分类" field="category" :rules="[{ required: true, message: '请选择二级分类' }]">
          <ASelect v-model="formData.category" :fallback-option="false" placeholder="请选择二级分类">
            <AOption v-for="item in listing2" :key="item.categoryId" :value="item.categoryId">{{ item.categoryName }}</AOption>
          </ASelect>
        </AFormItem>

        <AFormItem label="标题" field="title" :rules="[{ required: true, message: '请输入标题' }]">
          <AInput v-model="formData.title" />
        </AFormItem>

        <AFormItem label="封面" field="cover" help="图片尺寸: 课程封面(864x400 像素)；照片墙(800x480 像素)" :rules="[{ required: true, message: '请选择封面' }]" v-if="resourceConfig.includes('cover')">
          <OpenUpload v-model="formData.cover" />
        </AFormItem>

        <AFormItem label="URL" field="url" :rules="[{ required: true, message: '请输入 URL' }]" v-if="resourceConfig.includes('url')">
          <AInput v-model="formData.url" />
        </AFormItem>

        <AFormItem label="内容" field="content" help="Markdown 格式，请注意类似 ::Component.xxx 的内容是特殊组件，有特殊功能，请确认后再修改" :rules="[{ required: true, message: '请输入内容' }]" v-if="resourceConfig.includes('markdown')">
          <MarkdownEditor :height="500" break v-model="formData.content" />
        </AFormItem>

        <AFormItem label="内容" field="content" :rules="[{ required: true, message: '请输入内容' }]" v-else-if="resourceConfig.includes('text')">
          <ATextarea v-model="formData.content" />
        </AFormItem>

        <AFormItem label="顺序号" field="orderNum" :rules="[{ required: true, message: '请输入顺序号' }]">
          <AInputNumber v-model="formData.orderNum" />
        </AFormItem>

        <ADivider />

        <AFormItem>
          <ASpace>
            <OpenRouterButton to="/law/index">返回列表</OpenRouterButton>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
