
<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';

const { success } = useNotification();

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const targetListing = ref<any[]>([]);
const levelListing = ref<any[]>([]);
const form = ref({
  title: '',
  resourceStatus: '',
  realm: '',
  category: '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/v1/law/resource/page', {
    data: {
      resourceStatus: form.value.resourceStatus,
      title: form.value.title,
      realm: form.value.realm,
      category: form.value.category,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/v1/law/resource/delete', {
    data: {
      resourceId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const getTargetList = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/law/resource/category/list');

  if (isSuccess) {
    targetListing.value = data.list;
  }
}

getTargetList()

const changeTarget = async () => {
  form.value.category = '';

  refresh();

  if (!form.value.realm) {
    levelListing.value = [];
    return;
  }

  const { isSuccess, data } = await $fetch('/admin/v1/law/resource/category/list', {
    data: {
      parentId: form.value.realm,
    },
  });

  if (isSuccess) {
    levelListing.value = data.list;
  }
}

onActivated(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #search-before>
        <div class="mb-20px">
          <ARadioGroup v-model="form.realm" @change="changeTarget">
            <ARadio value="">
              <template #radio="{ checked }">
                <ATag :checked="checked" size="large" color="arcoblue" checkable>全部</ATag>
              </template>
            </ARadio>
            <ARadio v-for="item in targetListing" :key="item.categoryId" :value="item.categoryId">
              <template #radio="{ checked }">
                <ATag :checked="checked" size="large" color="arcoblue" checkable>{{ item.categoryName }}</ATag>
              </template>
            </ARadio>
          </ARadioGroup>
        </div>
      </template>

      <template #header>
        <AFormItem label="标题">
          <AInput v-model="form.title" allow-clear />
        </AFormItem>
        <AFormItem label="内容分类">
          <ASelect v-model="form.category">
            <AOption value="">全部</AOption>
            <AOption v-for="item in levelListing" :key="item.categoryId" :value="item.categoryId">{{ item.categoryName }}</AOption>
          </ASelect>
        </AFormItem>
        <AFormItem label="状态">
          <ASelect v-model="form.resourceStatus">
            <AOption value="">全部</AOption>
            <AOption :value="1">启用</AOption>
            <AOption :value="2">禁用</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton :to="`/law/add/${form.realm}`" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="标题" data-index="title" />
        <ATableColumn title="法律分类" header-cell-class="w-20%">
          <template #cell="{ record }">
            {{ record.realmName }} / {{ record.categoryName }}
          </template>
        </ATableColumn>
        <ATableColumn title="顺序号" data-index="orderNum" :width="100" />
        <ATableColumn title="状态" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.resourceStatus == 1" color="blue">启用</ATag>
            <ATag v-else-if="record.resourceStatus == 2">禁用</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="操作" :width="180">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/law/edit/${record.resourceId}`">编辑</OpenRouterButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.resourceId)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
