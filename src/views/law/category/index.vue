<script lang="ts" setup>
import { ref, onActivated, computed } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useCategoryStatus, useLayoutStatus } from './use';

const { success } = useNotification();
const categoryStatus = useCategoryStatus();
const layoutStatus = useLayoutStatus();

const listing = ref([]);
const loading = ref(false);
const search = ref();
const form = ref({
  categoryStatus: '',
  layoutState: '',
});
const parentId = ref('');
const parentName = ref('');
const isSecond = ref(false);

const refresh = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/law/resource/category/list', {
    data: {
      parentId: parentId.value,
      categoryStatus: form.value.categoryStatus,
      layoutState: form.value.layoutState,
    },
    loadingStatus: loading,
  });

  isSecond.value = !!parentId.value;

  if (isSuccess) {
    listing.value = data.list;
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/v1/law/resource/category/delete', {
    data: {
      categoryId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const gotoSecond = (id: string, name: string) => {
  parentId.value = id;
  parentName.value = name;
  search.value.refresh('reset');
}

const gotoFirst = () => {
  parentId.value = '';
  parentName.value = '';
  search.value.refresh('reset');
}

const title = computed(() => {
  return '法律分类' + (parentId.value ? ` - ${parentName.value}` : '');
});

const dataType = computed(() => isSecond.value ? 'second' : 'first');

const setVisibility = async (status: string, id: string) => {
  const { isSuccess } = await $fetch(`/admin/v1/law/resource/category/${status}`, {
    data: {
      categoryId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    search.value.refresh();
  }
}

onActivated(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :title="title" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="分类状态">
          <ASelect v-model="form.categoryStatus">
            <AOption value="">全部</AOption>
            <AOption v-for="item in categoryStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
          </ASelect>
        </AFormItem>
        <AFormItem label="显示状态">
          <ASelect v-model="form.layoutState">
            <AOption value="">全部</AOption>
            <AOption v-for="item in layoutStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <AButton @click="gotoFirst()" v-if="isSecond">
          <template #icon>
            <IconUndo />
          </template>
          返回一级分类列表
        </AButton>
        <OpenRouterButton :to="`/law/category/add/${dataType}/${parentId}`" type="primary">新建{{ isSecond ? '二级分类' : '一级分类' }}</OpenRouterButton>
      </template>

      <template #header-button-extra>
        <AButton @click="gotoFirst()" v-if="isSecond">
          <template #icon>
            <IconUndo />
          </template>
          返回一级分类列表
        </AButton>
      </template>

      <template #columns>
        <ATableColumn title="ID" data-index="categoryId" :width="150" />
        <ATableColumn :title="isSecond ? '二级分类' : '一级分类'" data-index="categoryName" />
        <ATableColumn title="序号" data-index="orderNum" :width="100" />

        <ATableColumn title="分类状态" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.categoryStatus == 1" color="blue">启用</ATag>
            <ATag v-else-if="record.categoryStatus == 2">禁用</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="显示状态" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.layoutState == 1" color="blue">显示</ATag>
            <ATag v-else-if="record.layoutState == 2">隐藏</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="操作" :width="isSecond ? 240 : 360">
          <template #cell="{ record }">
            <div class="button-actions">
              <AButton size="small" @click="gotoSecond(record.categoryId, record.categoryName)" v-if="!isSecond">查看二级分类</AButton>
              <AButton size="small" @click="setVisibility('show', record.categoryId)" v-confirm v-if="record.categoryStatus == 1 && record.layoutState == 2">显示</AButton>
              <AButton size="small" @click="setVisibility('hide', record.categoryId)" v-confirm v-else-if="record.categoryStatus == 1 && record.layoutState == 1">隐藏</AButton>
              <OpenRouterButton size="small" :to="`/law/category/edit/${dataType}/${record.categoryId}`">编辑</OpenRouterButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.categoryId)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
