<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { useCategoryStatus, useLayoutStatus } from './use';
import { useLanguage, onLanguageRouteUpdate } from '@/hooks/i18n';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },

  type: {
    type: String,
    default: 'add',
  },

  dataType: {
    type: String,
    default: 'first',
  },
});

const { success } = useNotification();
const categoryStatus = useCategoryStatus();
const layoutStatus = useLayoutStatus();
const language = useLanguage();
const router = useRouter();

const loading = ref(false);
const formData = ref<Record<string, any>>({
  parentId: '',
  categoryId: '',
  categoryName: '',
  orderNum: 1,
  categoryStatus: '',
  layoutState: '',
  resourceConfig: [],
});
const formRef = ref<FormInstance>();
const targetListing = ref<any[]>([]);
let dataLanguage = language;

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.type === 'edit' && (dataLanguage === language) ? '/admin/v1/law/resource/category/update' : '/admin/v1/law/resource/category/create', {
    data: {
      ...formData.value,
      resourceConfig: JSON.stringify(formData.value.resourceConfig),
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/law/category/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/law/resource/category/detail', {
    data: {
      categoryId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    dataLanguage = data.lc;

    formData.value = {
      parentId: data.parentId,
      categoryId: data.categoryId,
      categoryName: data.categoryName,
      categoryStatus: data.categoryStatus,
      layoutState: data.layoutState,
      orderNum: data.orderNum,
      resourceConfig: JSON.parse(data.resourceConfig || '[]'),
    };
  }
}

const getTargetList = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/law/resource/category/list');

  if (isSuccess) {
    targetListing.value = data.list;
  }
}

const getTitle = () => {
  return `${props.type === 'edit' ? '编辑' : '新增'}${props.dataType === 'second' ? '二级分类' : '一级分类'}`;
}

const init = async () => {
  if (props.dataType === 'second') {
    formData.value.parentId = props.id;

    await getTargetList();

    targetListing.value.find((item: any) => {
      if (item.categoryId === props.id) {
        formData.value.resourceConfig = JSON.parse(item.resourceConfig || '[]');
      }
    })
  }

  if (props.type === 'edit') {
    await getDetail();
  }
}

const configValidator = (value: string[], callback: (error?: string) => void) => {
  if (value.includes('text') && value.includes('markdown')) {
    callback('附加字段不能同时选择纯文本和 Markdown 文本');
  }
  else {
    callback();
  }
}

onLanguageRouteUpdate(() => {
  init();
});

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard :title="getTitle()" :show-i18n="props.type === 'edit'">
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" v-confirm>
        <AFormItem v-if="props.type !== 'edit'">
          <AAlert>请不要忘记输入英文内容！新建后，编辑这条信息，然后点击右上角【切换到英文内容】来输入英文内容！</AAlert>
        </AFormItem>

        <AFormItem>
          <AAlert>请不要随意选择附加字段，如有默认值请使用默认值，其他情况请与研发童鞋沟通后再选！</AAlert>
        </AFormItem>

        <AFormItem label="一级分类" field="parentId" :rules="[{ required: true, message: '请选择一级分类' }]" v-if="props.dataType === 'second'">
          <ASelect placeholder="请选择一级分类" :fallback-option="false" v-model="formData.parentId">
            <AOption v-for="item in targetListing" :key="item.categoryId" :value="item.categoryId">{{ item.categoryName }}</AOption>
          </ASelect>
        </AFormItem>

        <AFormItem label="名称" field="categoryName" :rules="[{ required: true, message: '请输入名称' }]">
          <AInput v-model="formData.categoryName" />
        </AFormItem>

        <AFormItem label="序号" field="orderNum" :rules="[{ required: true, message: '请输入序号' }]">
          <AInputNumber v-model="formData.orderNum" />
        </AFormItem>

        <AFormItem label="附加字段" field="resourceConfig" :rules="[{ required: props.dataType === 'second', message: '请选择附加字段' }, { validator: configValidator }]" help="附加字段是指在新建或编辑当前分类的法律内容详情时需要输入的字段">
          <ACheckboxGroup v-model="formData.resourceConfig">
            <ACheckbox value="cover">封面</ACheckbox>
            <ACheckbox value="url">URL</ACheckbox>
            <ACheckbox value="text">纯文本内容</ACheckbox>
            <ACheckbox value="markdown">Markdown 内容</ACheckbox>
          </ACheckboxGroup>
        </AFormItem>

        <template v-if="props.type === 'edit'">
          <AFormItem label="分类状态" field="categoryStatus">
            <ASelect v-model="formData.categoryStatus">
              <AOption v-for="item in categoryStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
            </ASelect>
          </AFormItem>

          <AFormItem label="显示状态" field="layoutState">
            <ASelect v-model="formData.layoutState">
              <AOption v-for="item in layoutStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
            </ASelect>
          </AFormItem>
        </template>

        <ADivider />

        <AFormItem>
          <ASpace>
            <OpenRouterButton to="/law/category/index">返回列表</OpenRouterButton>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
