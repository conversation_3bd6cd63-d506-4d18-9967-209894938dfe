<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import router from '@/router';
import { usePositionList, usePageType, useWeightList } from './use';
import { useLanguage, onLanguageRouteUpdate } from '@/hooks/i18n';

const props = defineProps({
  pageType: {
    type: String,
    default: 'council',
  },
  id: {
    type: String,
    default: '',
  },
  view: {
    type: Boolean,
    default: false,
  },
});
const formData = ref<Record<string, any>>({});

if (props.pageType === 'council') {
  formData.value = {
    weight: '1',
  };
}

const pageConfig = usePageType(props.pageType);
const formRef = ref<FormInstance>();
const { success } = useNotification();
const loading = ref(false);
const positionList = usePositionList(pageConfig.configKey);
// weight 这是代表分组的概念
const weightList = useWeightList();
const language = useLanguage();
let haveEn = false;

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id && (haveEn || language !== 'en') ? '/admin/committee/member/v1/edit' : '/admin/committee/member/v1/save', {
    data: {
      id: props.id,
      committeeType: pageConfig.committeeType,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push(`/about/${props.pageType}/index`);
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/committee/member/v1/info', {
    data: {
      id: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    haveEn = data.haveEn;

    formData.value = {
      name: data.name,
      resume: data.resume,
      position: data.position,
      title: data.title,
      photo: data.photo,
      intro: data.intro,
      weight: data.weight,
      sort: data.sort,
      // workplace: data.workplace,
      // note: data.note,
    };
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

onLanguageRouteUpdate(() => {
  init();
});

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard :show-i18n="!!props.id">
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" :disabled="props.view" v-confirm>
        <AFormItem v-if="!props.id">
          <AAlert>请不要忘记输入英文内容！新建后，编辑这条信息，然后点击右上角【切换到英文内容】来输入英文内容！</AAlert>
        </AFormItem>

        <AFormItem label="职位" field="position" :rules="[{ required: true, message: '请选择职位' }]" help="输入职位后回车可以创建列表中不存在的职位" v-if="pageConfig.configKey">
          <ASelect placeholder="请选择" v-model="formData.position" allow-create>
            <AOption v-for="item in positionList" :key="item" :value="item">{{ item }}</AOption>
          </ASelect>
        </AFormItem>

        <AFormItem :label="['security', 'cloud'].includes(props.pageType) ? '名称' : '姓名'" field="name" :rules="[{ required: true, message: '请输入内容' }]">
          <AInput v-model="formData.name" />
        </AFormItem>

        <!-- <AFormItem label="工作单位" field="workplace" :rules="[{ required: true, message: '请输入工作单位' }]" v-if="props.pageType !== 'security'">
          <AInput v-model="formData.workplace" />
        </AFormItem> -->

        <AFormItem
          :label="['security', 'cloud'].includes(props.pageType) ? 'LOGO' : '照片'"
          field="photo"
          :rules="[{ required: true, message: '请上传图片' }]"
          :help="['security', 'cloud'].includes(props.pageType) ? '图片尺寸 426x156 像素' : '图片尺寸 352x492 像素'"
        >
          <OpenUpload v-model="formData.photo" />
        </AFormItem>

        <template v-if="!['security', 'cloud'].includes(props.pageType)">
          <AFormItem label="人员简介" field="resume" :rules="[{ maxLength: 2000 }]" help="完全版本的简介，理事长直接显示，其他人员鼠标放上去显示">
            <ATextarea v-model="formData.resume" :max-length="2000" show-word-limit />
          </AFormItem>
          <AFormItem label="人员简介精简版" field="intro" :rules="[{ maxLength: 1500 }]" help="精简版人员简介，除了理事长以外，其他人员直接显示此简介">
            <ATextarea v-model="formData.intro" :max-length="1500" show-word-limit />
          </AFormItem>
          <!-- <AFormItem label="备注" field="note" :rules="[{ required: true, message: '请输入备注' }]">
            <AInput v-model="formData.note" />
          </AFormItem> -->
          <template v-if="props.pageType === 'council'">
            <AFormItem label="头衔" field="title" :rules="[{ maxLength: 300 }]" help="理事会成员的头衔">
              <AInput v-model="formData.title" :max-length="300" show-word-limit />
            </AFormItem>
            <AFormItem label="分组" field="weight" :rules="[{ required: true }]" help="用于前端展示，目前理事长和监事有单独分组，其他人请使用默认组">
              <ASelect v-model="formData.weight" :allow-create="false">
                <AOption v-for="item in weightList" :key="item.value" :value="item.value">{{ item.text }}</AOption>
              </ASelect>
            </AFormItem>
          </template>
        </template>

        <AFormItem label="顺序号" field="sort" :rules="[{ required: true, message: '请输入顺序号' }]">
          <AInputNumber v-model="formData.sort" />
        </AFormItem>

        <ADivider />

        <AFormItem :disabled="false">
          <ASpace>
            <OpenRouterButton :to="`/about/${props.pageType}/index`">返回列表</OpenRouterButton>
            <AButton html-type="submit" type="primary" :loading="loading" v-if="!props.view">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
