<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import useNotification from '@/hooks/notification';
import { $fetch } from '@/utils/fetch';
import { usePositionList, usePageType, useWeightList } from './use';

const props = defineProps({
  pageType: {
    type: String,
    default: 'council',
  },
});

const pageConfig = usePageType(props.pageType);
// weight 这是代表分组的概念
const weightList = useWeightList();
const listing = ref([]);
const pager = ref({});
const loading = ref(false);

const form = ref({
  name: '',
  position: '',
});

const positionList = usePositionList(pageConfig.configKey);
const search = ref();
const { success } = useNotification();

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/committee/member/v1/listPage', {
    data: {
      pageSize: 20,
      page,
      name: form.value.name,
      position: form.value.position,
      committeeType: pageConfig.committeeType,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.committeeMemberVO;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

onActivated(() => {
  search.value.refresh('reset');
});

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/committee/member/v1/remove', {
    data: {
      id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
}
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem :label="['security', 'cloud'].includes(props.pageType) ? '名称' : '姓名'">
          <AInput v-model="form.name" allow-clear />
        </AFormItem>

        <AFormItem label="职位" v-if="pageConfig.configKey">
          <ASelect v-model="form.position" placeholder="请选择">
            <AOption :value="''">全部</AOption>
            <AOption v-for="item in positionList" :key="item" :value="item">{{ item }}</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton :to="`/about/${props.pageType}/add`" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <!-- <ATableColumn title="id" data-index="id" /> -->
        <ATableColumn :title="['security', 'cloud'].includes(props.pageType) ? '名称' : '姓名'" data-index="name" />

        <ATableColumn :title="['security', 'cloud'].includes(props.pageType) ? 'LOGO' : '照片'" :width="300">
          <template #cell="{ record }">
            <OpenImage :src="record.photo" img-class="h-150px" />
          </template>
        </ATableColumn>

        <template v-if="!['security', 'cloud'].includes(props.pageType)">
          <ATableColumn title="职位" data-index="position" v-if="props.pageType !== 'mentor'" />
          <!-- <ATableColumn title="工作单位" data-index="workplace" /> -->
        </template>

        <template v-if="props.pageType === 'council'">
          <ATableColumn title="头衔" data-index="title" />
          <ATableColumn title="分组">
            <template #cell="{ record }">
              {{ weightList.find(item => item.value === record.weight)?.text }}
            </template>
          </ATableColumn>
        </template>

        <ATableColumn title="顺序号" data-index="sort" :width="100" />

        <ATableColumn title="操作" :width="230">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/about/${props.pageType}/view/${record.id}`">查看</OpenRouterButton>
              <OpenRouterButton size="small" :to="`/about/${props.pageType}/edit/${record.id}`">编辑</OpenRouterButton>
              <AButton type="primary" size="small" status="danger" @click="remove(record.id)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
