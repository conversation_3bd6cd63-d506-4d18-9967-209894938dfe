import { ref, type Ref } from 'vue';
import { $fetch } from '@/utils/fetch';

const getPositionList = async (key: string, list: Ref) => {
  if (!key) {
    return;
  }

  const { isSuccess, data } = await $fetch('/admin/system/config/v1/key', {
    data: {
      key,
    },
  });

  if (isSuccess) {
    list.value = data.positionList;
  }
};

const pageType: Record<string, any> = {
  council: {
    committeeType: 1,
    configKey: 'committee_member_councilJobList',
  },
  toc: {
    committeeType: 2,
    configKey: 'committee_member_tocJobList',
  },
  security: {
    committeeType: 3,
  },
  mentor: {
    committeeType: 4,
  },
  cloud: {
    committeeType: 5,
  },
}

// weight 这是代表分组的概念
const weightList = [
  { text: '默认组', value: '1' },
  { text: '理事长组', value: '2' },
  { text: '监事组', value: '3' },
];

export const usePositionList = (key: string) => {
  const list = ref<any[]>([]);

  getPositionList(key, list);

  return list;
};

export const usePageType = (type: string) => {
  return pageType[type] ?? pageType.council;
}

export const useWeightList = () => {
  return weightList;
}
