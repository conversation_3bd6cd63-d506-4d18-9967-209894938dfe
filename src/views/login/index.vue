<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store';
import { Modal } from '@arco-design/web-vue';
import { gotoLogin, getLoginRedirect } from '@/utils/auth';
import { onMounted } from 'vue';

const userStore = useUserStore();
const router = useRouter();

const props = defineProps({
  code: {
    type: String,
    default: '',
  },
  from: {
    type: String,
    default: '/',
  },
});

onMounted(async () => {
  const { isSuccess, msg } = await userStore.login(props.code, getLoginRedirect());

  if (isSuccess) {
    router.replace(props.from);
  }
  else {
    Modal.error({
      title: '登录失败',
      content: msg ?? '请联系管理员或尝试重新登录',
      okText: '重新登录',
      maskClosable: false,
      escToClose: false,
      closable: false,
      onOk() {
        gotoLogin(props.from);
      },
    });
  }
});
</script>

<template>
  <div class="absolute left-50% top-50% translate--50%">
    <ASpin tip="正在登录，请稍候..." />
  </div>
</template>

<style lang="scss" scoped>
</style>
