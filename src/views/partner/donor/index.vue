
<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { dialog } from '@/components/dialog';

const { success } = useNotification();

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const targetListing = ref<any[]>([]);
const levelListing = ref<any[]>([]);
const form = ref({
  donateLevel: '',
  donateTarget: '',
  donorName: '',
  donorStatus: '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/v1/donor/page', {
    data: {
      donorStatus: form.value.donorStatus,
      donorName: form.value.donorName,
      donateTarget: form.value.donateTarget,
      donateLevel: form.value.donateLevel,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/v1/donor/delete', {
    data: {
      donorId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const getTargetList = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/donor/category/list');

  if (isSuccess) {
    targetListing.value = data.list;
  }
}

getTargetList()

const changeTarget = async () => {
  form.value.donateLevel = '';

  if (!form.value.donateTarget) {
    levelListing.value = [];
    return
  }

  const { isSuccess, data } = await $fetch('/admin/v1/donor/category/list', {
    data: {
      parentId: form.value.donateTarget,
    },
  });

  if (isSuccess) {
    levelListing.value = data.list;
  }
}

onActivated(() => {
  search.value.refresh('reset');
});

// 打开弹窗新增标签
const add = async () => {
  const { button } = await dialog(import('./add-edit-window.vue'), {
    title: '新增捐赠人（中文）',
    width: 700,
  });

  if (button == 'ok') {
    search.value.refresh();
  }
}

// 打开弹窗编辑标签
const edit = async (id: string, language: string) => {
  const { button } = await dialog(import('./add-edit-window.vue'), {
    title: `编辑捐赠人（${language === 'zh' ? '中文' : '英文'}）`,
    width: 700,
    props: {
      id,
      language,
    },
  });

  if (button == 'ok') {
    search.value.refresh();
  }
};
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="名称">
          <AInput v-model="form.donorName" allow-clear />
        </AFormItem>
        <AFormItem label="捐赠目标">
          <ASelect v-model="form.donateTarget" @change="changeTarget">
            <AOption value="">全部</AOption>
            <AOption v-for="item in targetListing" :key="item.categoryId" :value="item.categoryId">{{ item.categoryName }}</AOption>
          </ASelect>
        </AFormItem>
        <AFormItem label="捐赠级别">
          <ASelect v-model="form.donateLevel">
            <AOption value="">全部</AOption>
            <AOption v-for="item in levelListing" :key="item.categoryId" :value="item.categoryId">{{ item.categoryName }}</AOption>
          </ASelect>
        </AFormItem>
        <AFormItem label="状态">
          <ASelect v-model="form.donorStatus">
            <AOption value="">全部</AOption>
            <AOption :value="1">启用</AOption>
            <AOption :value="2">禁用</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <AButton type="primary" @click="add">新建</AButton>
      </template>

      <template #columns>
        <ATableColumn title="名称" data-index="donorName" />

        <ATableColumn title="LOGO" :width="200">
          <template #cell="{ record }">
            <OpenImage :src="record.logo" img-class="w-full h-70px object-contain" />
          </template>
        </ATableColumn>

        <ATableColumn title="捐赠目标" data-index="donateTargetName" header-cell-class="w-15%" />
        <ATableColumn title="捐赠等级" data-index="donateLevelName" header-cell-class="w-10%" />
        <ATableColumn title="捐赠日期" data-index="donationDate" :width="130" />
        <ATableColumn title="顺序号" data-index="orderNum" :width="100" />
        <ATableColumn title="状态" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.donorStatus == 1" color="blue">启用</ATag>
            <ATag v-else-if="record.donorStatus == 2">禁用</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="操作" :width="120">
          <template #cell="{ record }">
            <div class="button-actions">
              <AButton size="small" @click="edit(record.donorId, 'zh')">编辑(中文)</AButton>
              <AButton size="small" @click="edit(record.donorId, 'en')">编辑(英文)</AButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.donorId)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
