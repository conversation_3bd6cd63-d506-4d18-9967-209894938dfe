<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },

  language: {
    type: String,
    default: 'zh',
  },
});

const { success } = useNotification();

const loading = ref(false);
const formData = ref({
  donorName: '',
  donateTarget: '',
  orderNum: 1,
  donationDate: '',
  logo: '',
  donateLevel: '',
});
const targetListing = ref<any[]>([]);
const levelListing = ref<any[]>([]);
const formRef = ref<FormInstance>();
let dataLanguage = props.language;

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/donor/detail', {
    data: {
      lc: props.language,
      donorId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    dataLanguage = data.lc;

    formData.value = {
      donorName: data.donorName,
      donateTarget: data.donateTarget,
      donationDate: data.donationDate,
      logo: data.logo,
      orderNum: data.orderNum,
      donateLevel: data.donateLevel,
    };
  }
};

const getTargetList = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/donor/category/list', {
    data: {
      lc: props.language,
    },
  });

  if (isSuccess) {
    targetListing.value = data.list;
  }
}

const getLevelList = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/donor/category/list', {
    data: {
      lc: props.language,
      parentId: formData.value.donateTarget,
    },
  });

  if (isSuccess) {
    levelListing.value = data.list;
  }
}

const init = async () => {
  if (props.id) {
    await getDetail();
    await getLevelList();
  }

  await getTargetList();
}

const changeTarget = () => {
  formData.value.donateLevel = '';
  getLevelList();
}

// 判断是否需要上传 LOGO
// 逻辑是：
// 1. 选定的捐赠目标和捐赠级别的 needLogo 都为 true 时才需要上传 LOGO
// 2. 如果捐赠级别没有选择，则只看捐赠目标的 needLogo
// 3. LOGO 字段默认是可选字段
// const requiredLogo = computed(
//   () =>
//     targetListing.value.some((item) => item.categoryId === formData.value.donateTarget && item.needLogo)
//     && (formData.value.donateLevel === '' || levelListing.value.some((item) => item.categoryId === formData.value.donateLevel && item.needLogo))
// );

init();

const dialogClickButton = async (button: string) => {
  if (button !== 'ok') {
    return;
  }

  const res = await formRef.value?.validate();

  if (res) {
    return false;
  }

  const { isSuccess } = await $fetch(props.id && (dataLanguage === props.language) ? '/admin/v1/donor/update' : '/admin/v1/donor/create', {
    data: {
      lc: props.language,
      donorId: props.id,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
  }

  return isSuccess;
}

defineExpose({
  dialogClickButton,
});
</script>

<template>
  <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData">
    <AFormItem label="捐赠人名称" field="donorName" :rules="[{ required: true, message: '请输入名称' }]">
      <AInput v-model="formData.donorName" />
    </AFormItem>

    <AFormItem label="LOGO" field="logo" :rules="[{ required: true, message: '请上传 LOGO' }]" help="图片尺寸: 380x140 像素">
      <OpenUpload v-model="formData.logo" :image-aspect-ratio="380 / 140" />
    </AFormItem>

    <AFormItem label="捐赠目标" field="donateTarget" :rules="[{ required: true, message: '请选择捐赠目标' }]">
      <ASelect v-model="formData.donateTarget" :fallback-option="false" placeholder="请选择捐赠目标" @change="changeTarget">
        <AOption v-for="item in targetListing" :key="item.categoryId" :value="item.categoryId">{{ item.categoryName }}</AOption>
      </ASelect>
    </AFormItem>

    <AFormItem label="捐赠级别" field="donateLevel" :rules="[{ required: true, message: '请选择捐赠级别' }]">
      <ASelect v-model="formData.donateLevel" :fallback-option="false" placeholder="请选择捐赠级别">
        <AOption v-for="item in levelListing" :key="item.categoryId" :value="item.categoryId">{{ item.categoryName }}</AOption>
      </ASelect>
    </AFormItem>

    <AFormItem label="捐赠日期" field="donationDate" :rules="[{ required: true, message: '请选择捐赠日期' }]">
      <ADatePicker v-model="formData.donationDate" />
    </AFormItem>

    <AFormItem label="顺序号" field="orderNum" :rules="[{ required: true, message: '请输入顺序号' }]">
      <AInputNumber v-model="formData.orderNum" />
    </AFormItem>
  </AForm>
</template>

<style lang="scss" scoped>
</style>
