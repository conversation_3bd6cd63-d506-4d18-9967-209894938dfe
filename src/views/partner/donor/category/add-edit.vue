<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { useCategoryStatus, useLayoutStatus } from './use';
import { useLanguage, onLanguageRouteUpdate } from '@/hooks/i18n';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },

  type: {
    type: String,
    default: 'add',
  },

  dataType: {
    type: String,
    default: 'target',
  },
});

const { success } = useNotification();
const categoryStatus = useCategoryStatus();
const layoutStatus = useLayoutStatus();
const language = useLanguage();
const router = useRouter();

const loading = ref(false);
const formData = ref({
  parentId: '',
  categoryId: '',
  categoryName: '',
  orderNum: 1,
  categoryStatus: '',
  layoutState: '',
  needLogo: true,
});
const formRef = ref<FormInstance>();
const targetListing = ref<any[]>([]);
let dataLanguage = language;

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.type === 'edit' && (dataLanguage === language) ? '/admin/v1/donor/category/update' : '/admin/v1/donor/category/create', {
    data: {
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/partner/donor/category/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/donor/category/detail', {
    data: {
      categoryId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    dataLanguage = data.lc;

    formData.value = {
      parentId: data.parentId,
      categoryId: data.categoryId,
      categoryName: data.categoryName,
      categoryStatus: data.categoryStatus,
      layoutState: data.layoutState,
      orderNum: data.orderNum,
      needLogo: data.needLogo,
    };
  }
}

const getTargetList = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/donor/category/list');

  if (isSuccess) {
    targetListing.value = data.list;
  }
}

const getTitle = () => {
  return `${props.type === 'edit' ? '编辑' : '新增'}${props.dataType === 'level' ? '捐赠级别' : '捐赠目标'}`;
}

const init = () => {
  if (props.dataType === 'level') {
    formData.value.parentId = props.id;
    getTargetList();
  }

  if (props.type === 'edit') {
    getDetail();
  }
}

onLanguageRouteUpdate(() => {
  init();
});

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard :title="getTitle()" :show-i18n="props.type === 'edit'">
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" v-confirm>
        <AFormItem v-if="props.type !== 'edit'">
          <AAlert>请不要忘记输入英文内容！新建后，编辑这条信息，然后点击右上角【切换到英文内容】来输入英文内容！</AAlert>
        </AFormItem>

        <AFormItem label="捐赠目标" field="parentId" :rules="[{ required: true, message: '请选择捐赠目标' }]" v-if="props.dataType === 'level'">
          <ASelect placeholder="请选择捐赠目标" :fallback-option="false" v-model="formData.parentId">
            <AOption v-for="item in targetListing" :key="item.categoryId" :value="item.categoryId">{{ item.categoryName }}</AOption>
          </ASelect>
        </AFormItem>

        <AFormItem label="名称" field="categoryName" :rules="[{ required: true, message: '请输入标题' }]">
          <AInput v-model="formData.categoryName" />
        </AFormItem>

        <AFormItem label="序号" field="orderNum" :rules="[{ required: true, message: '请输入序号' }]">
          <AInputNumber v-model="formData.orderNum" />
        </AFormItem>

        <template v-if="props.type === 'edit'">
          <AFormItem label="分类状态" field="categoryStatus">
            <ASelect v-model="formData.categoryStatus">
              <AOption v-for="item in categoryStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
            </ASelect>
          </AFormItem>

          <AFormItem label="显示状态" field="layoutState">
            <ASelect v-model="formData.layoutState">
              <AOption v-for="item in layoutStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
            </ASelect>
          </AFormItem>
        </template>

        <ADivider />

        <AFormItem>
          <ASpace>
            <OpenRouterButton to="/partner/donor/category/index">返回列表</OpenRouterButton>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
