<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import MarkdownEditor from '@/components/markdown-editor/index.vue';
import { marked } from 'marked';
import { parse, format, isSameYear, isSameMonth, isSameDay } from 'date-fns';
import { useLanguage, onLanguageRouteUpdate } from '@/hooks/i18n';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  view: {
    type: Boolean,
    default: false,
  },
});

const loading = ref(false);
const formData = ref<Record<string, any>>({
  title: '',
  intro: '',
  startTime: '',
  endTime: '',
  venue: '',
  date: '',
  times: [] as string[],
  dates: [] as string[],
  region: [],
  activityType: 1,
  activityUrl: '',
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();
const editor = ref();
const type = ref('1');
const region = ref([]);
const language = useLanguage();
let dataLanguage = language;

marked.use({
  renderer: {
    link(token) {
      const html = marked.Renderer.prototype.link.call(this, token);
      return html.replace(/^<a /, '<a target="_blank" ');
    },
  },
  gfm: true,
  breaks: true,
});

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const submitData: Record<string, any> = {
    title: formData.value.title,
    intro: formData.value.intro,
    venue: formData.value.venue,
    activityType: formData.value.activityType,
    activityUrl: formData.value.activityUrl,
  };

  if (formData.value.activityType == '1') {
    submitData.country = formData.value.region?.[0];
    submitData.province = formData.value.region?.[1];
    submitData.city = formData.value.region?.[2];
  }

  if (type.value === '1') {
    submitData.startTime = `${formData.value.date} 00:00:00`;
    submitData.endTime = `${formData.value.date} 23:59:59`;
  }
  else if (type.value === '2') {
    submitData.startTime = `${formData.value.date} ${formData.value.times[0]}`;
    submitData.endTime = `${formData.value.date} ${formData.value.times[1]}`;
  }
  else if (type.value === '3') {
    submitData.startTime = `${formData.value.dates[0]} 00:00:00`;
    submitData.endTime = `${formData.value.dates[1]} 23:59:59`;
  }

  const { isSuccess } = await $fetch(props.id && (dataLanguage === language) ? '/admin/v1/activity/update' : '/admin/v1/activity/create', {
    data: {
      activityId: props.id,
      ...submitData,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/events/index');
  }
};

const getDateType = (startDate: string, endDate: string) => {
  const start = parse(startDate, 'yyyy-MM-dd HH:mm:ss', new Date());
  const end = parse(endDate, 'yyyy-MM-dd HH:mm:ss', new Date());

  if (!isSameYear(start, end) || !isSameMonth(start, end) || !isSameDay(start, end)) {
    return '3';
  }

  if (format(start, 'HH:mm:ss') === '00:00:00' && format(end, 'HH:mm:ss') === '23:59:59') {
    return '1';
  }

  return '2';
};


const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/activity/detail', {
    data: {
      activityId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    dataLanguage = data.lc;

    type.value = getDateType(data.startTime, data.endTime);

    formData.value = {
      title: data.title,
      intro: data.intro,
      venue: data.venue,
      region: [data.countryCode, data.provinceCode, data.cityCode],
      activityType: data.activityType,
      activityUrl: data.activityUrl,
    };

    const startTime = parse(data.startTime, 'yyyy-MM-dd HH:mm:ss', new Date());
    const endTime = parse(data.endTime, 'yyyy-MM-dd HH:mm:ss', new Date());

    if (type.value === '1') {
      formData.value.date = format(startTime, 'yyyy-MM-dd');
    }
    else if (type.value === '2') {
      formData.value.date = format(startTime, 'yyyy-MM-dd');
      formData.value.times = [format(startTime, 'HH:mm:ss'), format(endTime, 'HH:mm:ss')];
    }
    else if (type.value === '3') {
      formData.value.dates = [format(startTime, 'yyyy-MM-dd'), format(endTime, 'yyyy-MM-dd')];
    }

    editor.value?.scrollToTop();
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

const getRegion = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/activity/region/selector');

  if (isSuccess) {
    region.value = data;
  }
};

getRegion();
// const isCurrentBeforeNow = (current: Date) => {
//   const now = new Date();
//   return isBefore(current, subDays(now, 1));
// };

init();

onLanguageRouteUpdate(() => {
  init();
});

const introValidator = (value: string, callback: (error?: string) => void) => {
  const introText = (marked.parse(value) as string).replace(/<[^>]+>/g, ''); // 去除HTML标签
  if (introText.length > 200) {
    callback('活动简介不能超过 200 个字');
  }
  else {
    callback();
  }
}

const regionValidator = (value: string[], callback: (error?: string) => void) => {
  if (!value?.[0] || !value?.[1] || !value?.[2]) {
    callback('请选择活动城市');
  }
  else {
    callback();
  }
}

const typeValidator = (value: string, callback: (error?: string) => void) => {
  if (!value) {
    callback('请选择地点类型');
  }
  else {
    callback();
  }
}

const urlValidator = (value: string, callback: (error?: string) => void) => {
  if (value && !/^https?:\/\//.test(value)) {
    callback('请输入正确的 URL');
  }
  else {
    callback();
  }
}
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard :show-i18n="!!props.id">
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" :disabled="props.view" v-confirm @submit="submit">
        <AFormItem v-if="!props.id">
          <AAlert>请不要忘记输入英文内容！新建后，编辑这条信息，然后点击右上角【切换到英文内容】来输入英文内容！</AAlert>
        </AFormItem>

        <AFormItem label="活动标题" field="title" :rules="[{ required: true, message: '请输入活动标题' }, { maxLength: 100 }]">
          <AInput v-model="formData.title" />
        </AFormItem>

        <AFormItem label="活动简介" field="intro" :rules="[{ required: true, message: '请输入活动简介' }, { validator: introValidator }]" help="Markdown 格式，活动简介不能超过 200 个字，尽量不要换行">
          <MarkdownEditor :height="200" v-model="formData.intro" :toolbar="[['bold', 'italic', 'strike', 'link']]" ref="editor" v-if="!props.view" />
          <!-- eslint-disable-next-line vue/no-v-html -->
          <div v-html="marked.parse(formData.intro)" class="markdown" v-else></div>
        </AFormItem>

        <AFormItem label="活动 URL" field="activityUrl" :rules="[{ validator: urlValidator }]">
          <AInput v-model="formData.activityUrl" />
        </AFormItem>

        <AFormItem label="活动类型">
          <ARadioGroup v-model="type">
            <ARadio value="1">单日全天活动</ARadio>
            <ARadio value="2">单日非全天活动</ARadio>
            <ARadio value="3">多日活动</ARadio>
          </ARadioGroup>
        </AFormItem>

        <template v-if="type === '1'">
          <AFormItem label="活动时间" field="date" :rules="[{ required: true, message: '请选择活动时间' }]">
            <!-- <ADatePicker v-model="formData.date" :disabled-date="(current) => isCurrentBeforeNow(current ?? new Date())" /> -->
            <ADatePicker v-model="formData.date" />
          </AFormItem>
        </template>
        <template v-else-if="type === '2'">
          <AFormItem label="活动日期" field="date" :rules="[{ required: true, message: '请选择活动日期' }]">
            <!-- <ADatePicker v-model="formData.date" :disabled-date="(current) => isCurrentBeforeNow(current ?? new Date())" /> -->
            <ADatePicker v-model="formData.date" />
          </AFormItem>
          <AFormItem label="活动时间" field="times" :rules="[{ required: true, message: '请选择活动时间' }]">
            <ATimePicker type="time-range" v-model="formData.times" />
          </AFormItem>
        </template>
        <template v-else-if="type === '3'">
          <AFormItem label="活动时间" field="dates" :rules="[{ required: true, message: '请选择活动时间' }]">
            <!-- <ARangePicker v-model="formData.dates" :disabled-date="(current) => isCurrentBeforeNow(current ?? new Date())" /> -->
            <ARangePicker v-model="formData.dates" />
          </AFormItem>
        </template>

        <AFormItem label="地点类型" field="activityType" :rules="[{ required: true, validator: typeValidator }]">
          <ARadioGroup v-model="formData.activityType">
            <ARadio :value="1">线下活动</ARadio>
            <ARadio :value="2">线上活动</ARadio>
          </ARadioGroup>
        </AFormItem>

        <AFormItem label="活动城市" field="region" :rules="[{ required: true, validator: regionValidator }]" v-if="formData.activityType == '1'">
          <ACascader v-model="formData.region" :options="region" :field-names="{ value: 'code', label: 'name' }" :fallback="false" :placeholder="props.view ? '无' : '请选择'" allow-search path-mode />
        </AFormItem>

        <AFormItem label="活动地点" field="venue" :rules="[{ maxLength: 100 }]" help="可以在这里填写具体的活动地点如某某大厦、某某会议室等">
          <AInput v-model="formData.venue" />
        </AFormItem>

        <ADivider />

        <AFormItem :disabled="false">
          <ASpace>
            <OpenRouterButton to="/events">返回列表</OpenRouterButton>
            <AButton html-type="submit" type="primary" :loading="loading" v-if="!props.view">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
:deep(.markdown) {
  p {
    margin: 0;
  }
}
</style>
