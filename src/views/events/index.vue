<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { parse, format, isSameYear, isSameMonth, isSameDay } from 'date-fns';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const region = ref([]);
const { success } = useNotification();

const form = ref({
  time: [],
  venue: '',
  title: '',
  regionCode: [],
  activityType: '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/v1/activity/page', {
    data: {
      title: form.value.title,
      startTime: form.value.time?.[0],
      endTime: form.value.time?.[1],
      venue: form.value.venue,
      country: form.value.regionCode?.[0],
      province: form.value.regionCode?.[1],
      city: form.value.regionCode?.[2],
      activityType: form.value.activityType,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/v1/activity/delete', {
    data: {
      activityId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const getRegion = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/activity/region/selector');

  if (isSuccess) {
    region.value = data;
  }
};

getRegion();

const formatDateRange = (startDate: string, endDate: string) => {
  const start = parse(startDate, 'yyyy-MM-dd HH:mm:ss', new Date());
  const end = parse(endDate, 'yyyy-MM-dd HH:mm:ss', new Date());

  if (!isSameYear(start, end) || !isSameMonth(start, end)) {
    return `${format(start, 'yyyy年M月d日')} - ${format(end, 'M月d日')}`;
  }

  if (!isSameDay(start, end)) {
    return `${format(start, 'yyyy年M月d日')} - ${format(end, 'd日')}`;
  }

  if (format(start, 'HH:mm:ss') === '00:00:00' && format(end, 'HH:mm:ss') === '23:59:59') {
    return `${format(start, 'yyyy年M月d日')}`;
  }

  return `${format(start, 'yyyy年M月d日 HH:mm')} - ${format(end, 'HH:mm')}`;
};

onActivated(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="活动标题">
          <AInput v-model="form.title" allow-clear />
        </AFormItem>
        <AFormItem label="地点类型">
          <ASelect v-model="form.activityType">
            <AOption value="">全部</AOption>
            <AOption :value="1">线下活动</AOption>
            <AOption :value="2">线上活动</AOption>
          </ASelect>
        </AFormItem>
        <AFormItem label="活动城市">
          <ACascader v-model="form.regionCode" :options="region" :field-names="{ value: 'code', label: 'name' }" placeholder="全部" allow-clear allow-search path-mode />
        </AFormItem>
        <AFormItem label="活动地点">
          <AInput v-model="form.venue" allow-clear />
        </AFormItem>
        <AFormItem label="活动时间">
          <ARangePicker show-time v-model="form.time" />
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/events/add" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="活动标题" data-index="title" />
        <ATableColumn title="活动城市">
          <template #cell="{ record }">
            <template v-if="record.activityType == 1">
              {{ record.cityName }}
            </template>
            <template v-else>
              <ATag color="blue">线上</ATag>
            </template>
          </template>
        </ATableColumn>
        <ATableColumn title="活动地点" data-index="venue" />

        <ATableColumn title="活动时间" :width="300">
          <template #cell="{ record }">
            {{ formatDateRange(record.startTime, record.endTime) }}
          </template>
        </ATableColumn>

        <ATableColumn title="创建时间" data-index="createTime" :width="180" />

        <ATableColumn title="操作" :width="260">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/events/view/${record.activityId}`">查看</OpenRouterButton>
              <OpenRouterButton size="small" :to="`/events/edit/${record.activityId}`">编辑</OpenRouterButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.activityId)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
