<script lang="ts" setup>
const props = defineProps({
  title: {
    type: String,
    default: '',
  },

  content: {
    type: String,
    default: '',
  },
});

defineExpose({
  // dialogClickButton,
  dialogOptions: {
    width: 1200,
    title: '预览邮件',
    buttons: { cancel: '关闭' },
  },
});
</script>

<template>
  <ACard :title="props.title" :bordered="false">
    <!-- eslint-disable-next-line vue/no-v-html -->
    <div v-html="props.content"></div>
  </ACard>
</template>

<style lang="scss" scoped>
</style>
