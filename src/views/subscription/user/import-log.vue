<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { $fetch } from '@/utils/fetch';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/email/v1/page/importHistory', {
    data: {
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

onMounted(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable sub-title="" button-text="刷新" :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #columns>
        <ATableColumn title="文件名称" data-index="fileName" />
        <!-- <ATableColumn title="操作结果" data-index="email" /> -->
        <ATableColumn title="操作人" data-index="createrName" />
        <ATableColumn title="操作时间" data-index="createdAt" />

        <ATableColumn title="操作" :width="200">
          <template #cell="{ record }">
            <div class="button-actions">
              <AButton size="small" :href="record.fileUrl" target="_blank">下载</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
