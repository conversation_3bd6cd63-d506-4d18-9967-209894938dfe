<script lang="ts" setup>
import { ref, onMounted, type PropType } from 'vue';
import { $fetch } from '@/utils/fetch';

const props = defineProps({
  params: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({}),
  },

  recipients: {
    type: Array as PropType<Record<string, any>[]>,
    default: () => [],
  },
});

const listing = ref<Record<string, any>[]>([]);
const loading = ref(false);
const pageNumber = ref(1);
const pagination = ref({
  pageSize: 20,
  total: 1,
  current: 1,
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/email/v1/page', {
    data: {
      ...props.params,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pagination.value.current = page;
    pagination.value.total = data.totalCount;
  }
}

onMounted(() => {
  if (props.recipients.length > 0) {
    listing.value = props.recipients;
  }
  else {
    refresh();
  }
});

const pageChange = (page: number) => {
  pageNumber.value = page;

  refresh(pageNumber.value);
}

defineExpose({
  dialogOptions: {
    width: 1200,
    title: '发送确认',
    buttons: { ok: '确认', cancel: '取消' },
  },
});
</script>

<template>
  <div>
    <ATypographyText type="danger" class="mb-10px block">
      邮件发送后将无法撤回，请再次确认相关信息是否正确！
    </ATypographyText>
    <ATable
      :data="listing"
      :pagination="pagination"
      row-key="id"
      @page-change="pageChange"
    >
      <template #columns>
        <ATableColumn title="用户名称" data-index="name" />
        <ATableColumn title="邮箱" data-index="email" />
      </template>
    </ATable>
  </div>
</template>

<style lang="scss" scoped>
</style>
