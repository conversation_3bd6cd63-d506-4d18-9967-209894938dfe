<script lang="ts" setup>
const props = defineProps({
  name: {
    type: String,
    default: '',
  },

  email: {
    type: String,
    default: '',
  },
});

defineExpose({
  // dialogClickButton,
  dialogOptions: {
    title: '查看用户',
    buttons: { cancel: '关闭' },
  },
});
</script>

<template>
  <ADescriptions size="large" :column="1">
    <ADescriptionsItem label="用户姓名" :span="1">
      {{ props.name }}
    </ADescriptionsItem>
    <ADescriptionsItem label="用户邮箱" :span="1">
      {{ props.email }}
    </ADescriptionsItem>
  </ADescriptions>
</template>

<style lang="scss" scoped>
</style>
