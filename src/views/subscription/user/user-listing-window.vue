<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { $fetch } from '@/utils/fetch';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const listing = ref<Record<string, any>[]>([]);
const loading = ref(false);
const pageNumber = ref(1);
const pagination = ref({
  pageSize: 20,
  total: 0,
  current: 1,
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/email/v1/history/recipients', {
    data: {
      id: props.id,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pagination.value.current = page;
    pagination.value.total = data.totalCount;
  }
}

onMounted(() => {
  refresh();
});

const pageChange = (page: number) => {
  pageNumber.value = page;

  refresh(pageNumber.value);
}

defineExpose({
  dialogOptions: {
    width: 1200,
    title: '收件人',
    buttons: { cancel: '关闭' },
  },
});
</script>

<template>
  <div>
    <ATable
      :data="listing"
      :pagination="pagination"
      row-key="id"
      @page-change="pageChange"
    >
      <template #columns>
        <ATableColumn title="用户名称" data-index="name" />
        <ATableColumn title="邮箱" data-index="email" />
      </template>
    </ATable>
  </div>
</template>

<style lang="scss" scoped>
</style>
