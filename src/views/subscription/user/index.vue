<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { dialog } from '@/components/dialog';

const listing = ref<Record<string, any>[]>([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success, warning } = useNotification();
const importing = ref(false);

const selectedKeys = ref<string[]>([]);

const form = ref({
  name: '',
  email: '',
  source: 0,
  time: [],
});

const sourceList: Record<string, any> = {
  1: '官网订阅',
  2: '后台导入',
  3: '校源行订阅',
}

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/email/v1/page', {
    data: {
      name: form.value.name,
      email: form.value.email,
      source: form.value.source,
      beginTime: form.value.time?.[0],
      endTime: form.value.time?.[1],
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (email: string) => {
  const { isSuccess } = await $fetch('/admin/email/v1/unSubscribe', {
    data: {
      email,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const sendEmail = async (isAll: boolean = false) => {
  if (!isAll && selectedKeys.value.length === 0) {
    warning('请至少选择一个用户');
    return;
  }

  const { button, data } = await dialog(import('./template-window.vue'));

  if (button !== 'ok') {
    return;
  }

  const { subject, content } = data;

  const params: Record<string, any> = {
    content,
    subject,
    operatorType: isAll ? 2 : 1,
  }

  if (isAll) {
    params.subscribeListRequest = {
      name: form.value.name,
      email: form.value.email,
      source: form.value.source,
      beginTime: form.value.time?.[0],
      endTime: form.value.time?.[1],
      page: 1,
      pageSize: 1000000,
    }
  }
  else {
    params.recipients = selectedKeys.value;
  }

  const { button: sendButton } = await dialog(import('./send-window.vue'), {
    props: {
      params: params.subscribeListRequest,
      recipients: listing.value.filter((item) => selectedKeys.value.includes(item.email)),
    },
  });

  if (sendButton !== 'ok') {
    return;
  }

  const { isSuccess } = await $fetch('/admin/email/v1/send', {
    data: params,
    loadingStatus: loading,
  });

  importing.value = false;

  if (isSuccess) {
    success('加入邮件发送队列成功');
    search.value.refresh();
  }
}

const importEmail = async (url: string) => {
  importing.value = true;

  const { isSuccess } = await $fetch('/admin/email/v1/subscribe/import', {
    data: {
      importFile: url,
    },
    loadingStatus: loading,
  });

  importing.value = false;

  if (isSuccess) {
    success('导入成功');
    search.value.refresh();
  }
}

onActivated(() => {
  search.value.refresh('reset');
});

const showUser = async (name: string, email: string) => {
  await dialog(import('./view-user-window.vue'), {
    props: {
      name,
      email,
    },
  });
}
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable
      :pager="pager"
      :loading="loading"
      @search="refresh"
      ref="search"
    >
      <template #header>
        <AFormItem label="用户姓名">
          <AInput v-model="form.name" allow-clear />
        </AFormItem>
        <AFormItem label="用户邮箱">
          <AInput v-model="form.email" allow-clear />
        </AFormItem>
        <AFormItem label="订阅时间">
          <ARangePicker v-model="form.time" />
        </AFormItem>
        <AFormItem label="来源">
          <ASelect v-model="form.source">
            <AOption :value="0">全部</AOption>
            <AOption :value="1">官网订阅</AOption>
            <AOption :value="2">后台导入</AOption>
            <AOption :value="3">校源行订阅</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <ADropdown position="bl">
          <AButton type="primary">
            发送邮件
            <span class="ml-8px"><IconDown /></span>
          </AButton>
          <template #content>
            <ADoption @click="sendEmail()">当前选中用户</ADoption>
            <ADoption @click="sendEmail(true)">当前条件下所有用户</ADoption>
          </template>
        </ADropdown>

        <OpenUpload simple :limit="0" @success="importEmail" accept=".csv, .xls, .xlsx" :max-file-size="1000 * 1000 * 50">
          <template #upload-button="{ uploading: uploadLoading }">
            <AButton :loading="uploadLoading || importing">导入</AButton>
          </template>
        </OpenUpload>
      </template>

      <template #header-button-extra>
        <OpenRouterButton to="/subscription/user/send-history">发送历史</OpenRouterButton>
        <OpenRouterButton to="/subscription/user/import-log">导入日志</OpenRouterButton>
      </template>

      <template #table="{ pageChange, bordered, loading: tableLoading, pagination }">
        <ATable
          :bordered="bordered"
          :loading="tableLoading"
          :data="listing"
          row-key="email"
          :row-selection="{
            type: 'checkbox',
            showCheckedAll: true,
            onlyCurrent: true,
          }"
          :pagination="pagination"
          v-model:selected-keys="selectedKeys"
          @page-change="pageChange"
        >
          <template #columns>
            <ATableColumn title="用户姓名" data-index="name" />
            <ATableColumn title="用户邮箱" data-index="email" />
            <ATableColumn title="订阅时间" data-index="createdAt" />
            <ATableColumn title="来源" :width="200">
              <template #cell="{ record }">
                {{ sourceList[record.source] }}
              </template>
            </ATableColumn>

            <ATableColumn title="操作" :width="200">
              <template #cell="{ record }">
                <div class="button-actions">
                  <AButton size="small" @click="showUser(record.name, record.email)">查看</AButton>
                  <AButton type="primary" status="danger" size="small" @click="remove(record.email)" v-confirm>删除</AButton>
                </div>
              </template>
            </ATableColumn>
          </template>
        </ATable>
      </template>

    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
