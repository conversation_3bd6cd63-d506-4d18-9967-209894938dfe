<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { marked } from 'marked';

const listing = ref<Record<string, any>[]>([]);
const loading = ref(false);
const pageNumber = ref(1);
const form = ref({
  name: '',
});
const pagination = ref({
  pageSize: 20,
  total: 0,
  current: 1,
});
const selectedKeys = ref<string[]>([]);
const { warning } = useNotification();

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/email/template/v1/page', {
    data: {
      name: form.value.name,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pagination.value.current = page;
    pagination.value.total = data.totalCount;
  }
}

onMounted(() => {
  refresh();
});

const getDetail = async (id: string) => {
  const { isSuccess, data } = await $fetch('/admin/email/template/v1/detail', {
    data: {
      id,
    },
  });

  if (isSuccess) {
    return data;
  }
  else {
    return {};
  }
}

const dialogClickButton = async (button: string) => {
  if (button !== 'ok') {
    return true;
  }

  if (button === 'ok' && selectedKeys.value.length === 0) {
    warning('请选择一个模版');
    return false;
  }

  const detail = await getDetail(selectedKeys.value[0]);

  return {
    close: true,
    subject: detail.title,
    content: marked.parse(detail.content ?? ''),
  };
}

const handleSubmit = () => {
  refresh();
}

const pageChange = (page: number) => {
  pageNumber.value = page;

  refresh(pageNumber.value);
}

defineExpose({
  dialogClickButton,
  dialogOptions: {
    width: 1200,
    title: '选择模版',
    buttons: { ok: '确认', cancel: '取消' },
  },
});
</script>

<template>
  <div>
    <AForm layout="inline" :model="{}" @submit="handleSubmit">
      <AFormItem label="模版名称">
        <AInput v-model="form.name" allow-clear />
      </AFormItem>
      <AFormItem hide-label>
        <ASpace>
          <AButton type="primary" html-type="submit">查询</AButton>
        </ASpace>
      </AFormItem>
    </AForm>

    <ATable
      :data="listing"
      :pagination="pagination"
      :row-selection="{
        type: 'radio',
        showCheckedAll: false,
        onlyCurrent: true,
      }"
      v-model:selected-keys="selectedKeys"
      row-key="id"
      @page-change="pageChange"
    >
      <template #columns>
        <ATableColumn title="模板名称" data-index="name" />
        <ATableColumn title="邮件标题" data-index="title" />
      </template>
    </ATable>
  </div>
</template>

<style lang="scss" scoped>
</style>
