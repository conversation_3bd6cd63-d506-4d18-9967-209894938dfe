<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { $fetch } from '@/utils/fetch';
import { dialog } from '@/components/dialog';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/email/v1/historyPage', {
    data: {
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const showUser = async (id: string) => {
  await dialog(import('./user-listing-window.vue'), {
    props: {
      id,
    },
  });
}

const showPreview = async (title: string, content: string) => {
  await dialog(import('./preview-window.vue'), {
    props: {
      title,
      content,
    },
  });
}

onMounted(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable sub-title="" button-text="刷新" :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #columns>
        <ATableColumn title="邮件标题" data-index="subject" />
        <ATableColumn title="发送时间" data-index="createdAt" />
        <ATableColumn title="操作人" data-index="createrName" />

        <ATableColumn title="操作" :width="200">
          <template #cell="{ record }">
            <div class="button-actions">
              <AButton size="small" @click="showPreview(record.subject, record.content)">预览</AButton>
              <AButton size="small" @click="showUser(record.id)">收件人</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
