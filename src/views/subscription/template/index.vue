<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { dialog } from '@/components/dialog';
import { marked } from 'marked';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success } = useNotification();

const form = ref({
  name: '',
  createrName: '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/email/template/v1/page', {
    data: {
      name: form.value.name,
      createrName: form.value.createrName,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/email/template/v1/remove', {
    data: {
      id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

onActivated(() => {
  search.value.refresh('reset');
});

const showPreview = async (id: string) => {
  const { isSuccess, data } = await $fetch('/admin/email/template/v1/detail', {
    data: {
      id,
    },
  });

  if (isSuccess) {
    await dialog(import('../user/preview-window.vue'), {
      props: {
        title: data.title,
        content: marked.parse(data.content),
      },
    });
  }
}
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="模版名称">
          <AInput v-model="form.name" allow-clear />
        </AFormItem>
        <AFormItem label="创建人">
          <AInput v-model="form.createrName" allow-clear />
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/subscription/template/add" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="模板名称" data-index="name" />
        <ATableColumn title="邮件标题" data-index="title" />
        <ATableColumn title="创建人" data-index="createrName" />
        <ATableColumn title="创建时间" data-index="createdAt" />

        <ATableColumn title="操作" :width="260">
          <template #cell="{ record }">
            <div class="button-actions">
              <AButton size="small" @click="showPreview(record.id)">预览</AButton>
              <OpenRouterButton size="small" :to="`/subscription/template/edit/${record.id}`">编辑</OpenRouterButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.id)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
