<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import MarkdownEditor from '@/components/markdown-editor/index.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref({
  name: '',
  title: '',
  content: '',
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id ? '/admin/email/template/v1/edit' : '/admin/email/template/v1/add', {
    data: {
      id: props.id,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/subscription/template/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/email/template/v1/detail', {
    data: {
      id: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      name: data.name,
      title: data.title,
      content: data.content,
    };
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit">
        <AFormItem label="模版名称" field="name" :rules="[{ required: true, message: '请输入模版名称' }]">
          <AInput v-model="formData.name" />
        </AFormItem>

        <AFormItem label="邮件标题" field="title" :rules="[{ required: true, message: '请输入邮件标题' }]">
          <AInput v-model="formData.title" />
        </AFormItem>

        <AFormItem label="邮件内容" field="content" :rules="[{ required: true, message: '请输入邮件内容' }]" help="Markdown 格式">
          <MarkdownEditor :height="500" v-model="formData.content" />
        </AFormItem>

        <ADivider />

        <AFormItem>
          <ASpace>
            <OpenRouterButton to="/subscription/template/index">返回列表</OpenRouterButton>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
