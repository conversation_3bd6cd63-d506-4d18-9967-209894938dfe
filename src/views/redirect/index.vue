<script lang="ts" setup>
import { useRouter } from 'vue-router';

const router = useRouter();
const props = defineProps({
  path: {
    type: String,
    default: '/',
  },

  other: {
    type: String,
    default: '{}',
  },
});

let query = {};

try {
  query = JSON.parse(props.other);
}
catch (e) {
}

router.replace({ path: props.path, query: query });
</script>

<template>
  <div></div>
</template>

<style lang="scss" scoped>
</style>
