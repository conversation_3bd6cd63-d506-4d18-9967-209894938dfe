<script lang="ts" setup>
import { $fetch } from '@/utils/fetch';
import { watch, computed, ref } from 'vue';
import { marked } from 'marked';
import { onLanguageRouteUpdate } from '@/hooks/i18n';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const detail = ref<Record<string, any>>({});

const statusMap = {
  1: '捐赠期',
  2: '孵化期',
  3: '毕业期',
};
const keyMap: Record<string, any> = {
  status: {
    type: 'enum',
    text: '项目分类',
    enum: statusMap,
  },
  pTitle: '父项目',
  cardLogo: {
    type: 'image',
    text: '项目 LOGO',
  },
  cardSidebar: {
    type: 'image',
    text: '卡片顶部图片',
  },
  content: {
    type: 'markdown',
    text: '项目内容',
  },
  detailLogo: {
    type: 'image',
    text: '详情页 LOGO',
  },
  detailBanner: {
    type: 'image',
    text: '详情页 Banner',
  },
  label: {
    type: 'array',
    text: '项目 Tag',
  },
  donorSubject: '捐赠主体',
  contributeSubject: '贡献主体',
  techFeatures: '技术特点',
  projectSponsor: '项目 Sponsor',
  projectValue: '项目价值',
  intro: '项目简介',
  projectDownload: '下载项目 URL',
  projectCode: '项目代码 URL',
  externalLink: '项目社区',
  commits: 'Commit 数量',
  star: 'Star 数量',
  contributor: 'Contributor 数量',
  watch: 'Watch 数量',
  fork: 'Fork 数量',
  pr: 'PR 数量',
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/project/v1/info', {
    data: {
      id: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    detail.value = data;
  }
}

watch(() => props.id, () => {
  getDetail();
});

const descriptions = computed(() => {
  const result = [];

  for (const key in keyMap) {
    result.push({
      label: typeof keyMap[key] === 'string' ? keyMap[key] : keyMap[key].text,
      value: {
        type: typeof keyMap[key] === 'string' ? 'string' : keyMap[key].type,
        data: detail.value[key],
        extra: keyMap[key],
      },
    });
  }

  return result;
});

const renderMarkdown = (content: string) => {
  return content ? marked.parse(content) : '';
};

onLanguageRouteUpdate(() => {
  getDetail();
});

getDetail();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard show-i18n>
      <ADescriptions :title="detail.title" size="large" :data="(descriptions as any)" :column="1">
        <template #value="{ value }">
          <ASkeleton v-if="loading" animation>
            <ASkeletonLine :widths="['200px']" :rows="1" />
          </ASkeleton>
          <div v-else>
            <template v-if="typeof value === 'string' || value.type === 'string'">
              {{ value.data }}
            </template>
            <template v-else-if="value.type === 'image'">
              <img :src="value.data" alt="" class="max-h-300px max-w-full">
            </template>
            <template v-else-if="value.type === 'enum'">
              {{ value.extra.enum[value.data] }}
            </template>
            <template v-else-if="value.type === 'markdown'">
              <!-- eslint-disable-next-line vue/no-v-html -->
              <div v-html="renderMarkdown(value.data)"></div>
            </template>
            <template v-else-if="value.type === 'array'">
              <p v-for="(item, index) in value.data" :key="index" class="m-0 mb-10px">
                {{ item }}
              </p>
            </template>
          </div>
        </template>
      </ADescriptions>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
:deep() {
  .arco-descriptions-item-label {
    vertical-align: top;
  }

  .arco-descriptions-item-value-block {
    white-space: normal;
  }
}
</style>
