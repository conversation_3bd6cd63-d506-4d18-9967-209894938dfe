<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import { useStatusList } from './use';
import { useLanguage, onLanguageRouteUpdate } from '@/hooks/i18n';
import MarkdownEditor from '@/components/markdown-editor/index.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const step = ref(1);
const loading = ref(false);
const projectList = ref<any[]>([]);
const statusList = useStatusList();
const formData1 = ref({
  status: '',
  pid: '',
  title: '',
  cardLogo: '',
  content: '',
});
const formData2 = ref({
  detailLogo: '',
  detailBanner: '',
  label: [],
  donorSubject: '',
  contributeSubject: '',
  techFeatures: '',
  projectSponsor: '',
  projectValue: '',
  projectDownload: '',
  projectCode: '',
  externalLink: '',
  cardSidebar: '',
  commits: '',
  star: '',
  contributor: '',
  watch: '',
  fork: '',
  pr: '',
  sort: 0,
  intro: '',
  color: '',
  projectExtInfo: {
    additionalLinkText: '',
    additionalLinkUrl: '',
    additionalLinkImage: '',
  },
});
const formRef = ref<FormInstance[]>([]);
const submitSuccess = ref(false);
const createdId = ref('');
const editor = ref();
const language = useLanguage();
let haveEn = false;

const getProjectList = async () => {
  const { isSuccess, data } = await $fetch('/admin/project/v1/listPage', {
    data: {
      pageSize: 1000,
    },
  });

  if (isSuccess) {
    projectList.value = data.projectVO;
  }
};

const resetForm = () => {
  formRef.value.forEach(item => item.resetFields());
}

const submit = async () => {
  const res = await formRef.value[step.value - 1].validate();

  if (res) {
    return;
  }

  const { isSuccess, data } = await $fetch(props.id && (haveEn || language !== 'en') ? '/admin/project/v1/edit' : '/admin/project/v1/add', {
    data: {
      id: props.id,
      ...formData1.value,
      ...formData2.value,
    },
    loadingStatus: loading,
    errorMessage: false,
  });

  submitSuccess.value = isSuccess;

  if (isSuccess) {
    createdId.value = props.id ? props.id : data;
    resetForm();
  }

  step.value++;
};

const next = async () => {
  const res = await formRef.value[step.value - 1].validate();

  if (!res) {
    step.value++;
  }
}

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/project/v1/info', {
    data: {
      id: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    haveEn = data.haveEn;

    formData1.value = {
      status: data.status,
      pid: '',
      title: data.title,
      cardLogo: data.cardLogo,
      content: data.content,
    };
    formData2.value = {
      donorSubject: data.donorSubject,
      contributeSubject: data.contributeSubject,
      techFeatures: data.techFeatures,
      projectSponsor: data.projectSponsor,
      projectValue: data.projectValue,
      projectDownload: data.projectDownload,
      projectCode: data.projectCode,
      externalLink: data.externalLink,
      detailLogo: data.detailLogo,
      detailBanner: data.detailBanner,
      cardSidebar: data.cardSidebar,
      label: data.label ?? [],
      commits: data.commits,
      star: data.star,
      contributor: data.contributor,
      watch: data.watch,
      fork: data.fork,
      pr: data.pr,
      sort: data.sort,
      intro: data.intro,
      color: data.color,
      projectExtInfo: data.projectExtInfo || {},
    };

    editor.value.scrollToTop();
  }
}

const init = () => {
  getProjectList();

  if (props.id) {
    getDetail();
  }
}

init();

onLanguageRouteUpdate(() => {
  init();
});

const positiveIntegerRule = (value: string | undefined, callback: (error?: string) => void) => {
  if (value === undefined) {
    callback();
    return;
  }

  const intValue = parseInt(value, 10);

  if (!/^\d+$/.test(value) || isNaN(intValue) || intValue < 1) {
    callback('请输入正整数');
  }
  else {
    callback();
  }
};

const arrayLengthRule = (length: number, operator?: '>' | '>=' | '<' | '<=' | '=') => {
  const operators: Record<string, { fn: (a: number, b: number) => boolean, text: string }> = {
    '>': {
      text: '大于',
      fn: (a, b) => a > b,
    },
    '>=': {
      text: '大于等于',
      fn: (a, b) => a >= b,
    },
    '<': {
      text: '小于',
      fn: (a, b) => a < b,
    },
    '<=': {
      text: '小于等于',
      fn: (a, b) => a <= b,
    },
    '=': {
      text: '等于',
      fn: (a, b) => a == b,
    },
  };

  return (value: any[] | undefined, callback: (error?: string) => void) => {
    if (value === undefined) {
      callback();
      return;
    }

    const realOperator = operator || '=';
    const compareFn = operators[realOperator].fn;

    if (compareFn(value.length, length)) {
      callback();
    }
    else {
      callback(`数量必须${operators[realOperator].text} ${length}`);
    }
  };
}
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard :show-i18n="!!props.id">
      <ASpin :loading="loading" class="w-full">
        <div class="flex flex-col items-center py-64px">
          <ASteps v-model:current="step" line-less class="mb-76px w-900px">
            <AStep description="创建新的项目">项目基本信息</AStep>
            <AStep description="输入详细的项目信息">填写项目详情</AStep>
            <AStep description="成功创建新项目">完成创建</AStep>
          </ASteps>

          <AForm class="w-1000px" auto-label-width scroll-to-first-error :ref="(el: Record<string, any> | Element | null) => (formRef[0] = el as FormInstance)" :model="formData1" v-show="step === 1">
            <AFormItem v-if="!props.id">
              <AAlert>请不要忘记输入英文内容！新建后，编辑这条信息，然后点击右上角【切换到英文内容】来输入英文内容！</AAlert>
            </AFormItem>

            <AFormItem label="项目分类" field="status" :rules="[{ required: true, message: '请选择分类' }]">
              <ASelect placeholder="请选择" v-model="formData1.status">
                <AOption v-for="item in statusList" :key="item.projectStatusCode" :value="item.projectStatusCode">{{ item.projectStatusName }}</AOption>
              </ASelect>
            </AFormItem>

            <AFormItem label="父项目" field="pid">
              <ASelect allow-search v-model="formData1.pid">
                <AOption value="">(无)</AOption>
                <AOption v-for="item in projectList" :key="item.id" :value="item.id">{{ item.title }}</AOption>
              </ASelect>
            </AFormItem>

            <AFormItem label="项目名称" field="title" :rules="[{ required: true, message: '请输入项目名称' }, { maxLength: 50 }]">
              <AInput v-model.trim="formData1.title" :max-length="50" allow-clear show-word-limit />
            </AFormItem>

            <AFormItem label="项目 LOGO" field="cardLogo" help="图片尺寸: 130x36 像素">
              <OpenUpload v-model="formData1.cardLogo" :image-aspect-ratio="130 / 36" />
            </AFormItem>

            <AFormItem label="项目内容" field="content">
              <MarkdownEditor :height="800" v-model="formData1.content" ref="editor" />
            </AFormItem>

            <AFormItem class="mt-20px">
              <AButton type="primary" @click="next">下一步</AButton>
            </AFormItem>
          </AForm>

          <AForm class="w-1000px" auto-label-width scroll-to-first-error :ref="(el: Record<string, any> | Element | null) => (formRef[1] = el as FormInstance)" :model="formData2" v-show="step === 2" @submit="submit" v-confirm>
            <AFormItem label="卡片顶部图片" field="cardSidebar" help="图片尺寸: 430x88 像素">
              <OpenUpload v-model="formData2.cardSidebar" :image-aspect-ratio="430 / 88" />
            </AFormItem>

            <AFormItem label="详情页 LOGO" field="detailLogo" help="图片尺寸: 386x160 像素">
              <OpenUpload v-model="formData2.detailLogo" :image-aspect-ratio="386 / 160" />
            </AFormItem>

            <AFormItem label="详情页 Banner" field="detailBanner">
              <OpenUpload v-model="formData2.detailBanner" />
            </AFormItem>

            <AFormItem label="详情页标题文字颜色" field="color">
              <ASelect v-model="formData2.color">
                <AOption value="#ffffff">浅色</AOption>
                <AOption value="#2d2d2d">深色</AOption>
              </ASelect>
            </AFormItem>

            <AFormItem label="捐赠主体" field="donorSubject" :rules="[{ maxLength: 150 }]">
              <AInput v-model.trim="formData2.donorSubject" :max-length="150" allow-clear show-word-limit />
            </AFormItem>

            <AFormItem label="贡献主体" field="contributeSubject" :rules="[{ maxLength: 150 }]">
              <AInput v-model.trim="formData2.contributeSubject" :max-length="150" allow-clear show-word-limit />
            </AFormItem>

            <AFormItem label="技术特点" field="techFeatures" :rules="[{ required: true, message: '请输入技术特点' }, { maxLength: 100 }]">
              <AInput v-model.trim="formData2.techFeatures" :max-length="100" allow-clear show-word-limit />
            </AFormItem>

            <AFormItem label="项目 Sponsor" field="projectSponsor" :rules="[{ required: true, message: '请输入项目 Sponsor' }, { maxLength: 50 }]">
              <AInput v-model.trim="formData2.projectSponsor" :max-length="50" allow-clear show-word-limit />
            </AFormItem>

            <AFormItem label="项目价值" field="projectValue" :rules="[{ required: true, message: '请输入项目价值' }, { maxLength: 200 }]">
              <AInput v-model.trim="formData2.projectValue" :max-length="200" allow-clear show-word-limit />
            </AFormItem>

            <AFormItem label="项目简介" field="intro" :rules="[{ maxLength: 500 }]">
              <ATextarea v-model.trim="formData2.intro" :max-length="500" allow-clear show-word-limit />
            </AFormItem>

            <AFormItem label="项目 Tag" field="label" help="按回车键完成一个 Tag 的输入，最多输入 3 个 Tag" :rules="[{ type: 'array', validator: arrayLengthRule(3, '<=') }]">
              <AInputTag v-model="formData2.label" />
            </AFormItem>

            <AFormItem label="下载项目 URL" field="projectDownload">
              <AInput v-model="formData2.projectDownload" />
            </AFormItem>

            <AFormItem label="项目代码 URL" field="projectCode">
              <AInput v-model="formData2.projectCode" />
            </AFormItem>

            <AFormItem label="项目社区" field="externalLink">
              <AInput v-model="formData2.externalLink" />
            </AFormItem>

            <AFormItem label="附加链接文本" field="projectExtInfo.additionalLinkText">
              <AInput v-model="formData2.projectExtInfo.additionalLinkText" />
            </AFormItem>

            <AFormItem label="附加链接图片" field="projectExtInfo.additionalLinkImage" help="图片尺寸: 112x112 像素">
              <OpenUpload v-model="formData2.projectExtInfo.additionalLinkImage" :image-aspect-ratio="112 / 112" />
            </AFormItem>

            <AFormItem label="附加链接 URL" field="projectExtInfo.additionalLinkUrl">
              <AInput v-model="formData2.projectExtInfo.additionalLinkUrl" />
            </AFormItem>

            <AFormItem label="Commit 数量" field="commits" :rules="[{ validator: positiveIntegerRule }]">
              <AInput v-model.trim="formData2.commits" />
            </AFormItem>

            <AFormItem label="Star 数量" field="star" :rules="[{ validator: positiveIntegerRule }]">
              <AInput v-model.trim="formData2.star" />
            </AFormItem>

            <AFormItem label="Contributor 数量" field="contributor" :rules="[{ validator: positiveIntegerRule }]">
              <AInput v-model.trim="formData2.contributor" />
            </AFormItem>

            <AFormItem label="Watch 数量" field="watch" :rules="[{ validator: positiveIntegerRule }]">
              <AInput v-model.trim="formData2.watch" />
            </AFormItem>

            <AFormItem label="Fork 数量" field="fork" :rules="[{ validator: positiveIntegerRule }]">
              <AInput v-model.trim="formData2.fork" />
            </AFormItem>

            <AFormItem label="PR 数量" field="pr" :rules="[{ validator: positiveIntegerRule }]">
              <AInput v-model.trim="formData2.pr" />
            </AFormItem>

            <AFormItem label="序号" field="sort" :rules="[{ type: 'number', message: '请输入大于等于 0 的数字', min: 0 }]" help="用于项目排序，数值越小越靠前">
              <AInputNumber v-model="formData2.sort" />
            </AFormItem>

            <AFormItem class="mt-20px">
              <ASpace>
                <AButton @click="step--">上一步</AButton>
                <AButton html-type="submit" type="primary">下一步</AButton>
              </ASpace>
            </AFormItem>
          </AForm>

          <div class="text-center" v-show="step === 3">
            <template v-if="submitSuccess">
              <AResult status="success" title="提交成功" subtitle="项目创建/修改成功！" />
              <ASpace :size="16">
                <OpenRouterButton to="/project" v-if="props.id">返回列表</OpenRouterButton>
                <AButton @click="step = 1" v-else>再次创建</AButton>
                <OpenRouterButton type="primary" :to="`/project/view/${createdId}`">查看详情</OpenRouterButton>
              </ASpace>
            </template>
            <template v-else>
              <AResult status="error" title="提交失败" subtitle="项目创建失败，请重试！" />
              <ASpace :size="16">
                <OpenRouterButton to="/project">返回列表</OpenRouterButton>
                <AButton type="primary" @click="step = 1">重新提交</AButton>
              </ASpace>
            </template>
          </div>
        </div>
      </ASpin>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
