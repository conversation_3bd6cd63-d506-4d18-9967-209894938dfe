<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';

const loading = ref(false);
const formData = ref({
  pageDesc: {
    'zh-CN': '',
    'en-US': '',
  },
  titleDesc1: {
    'zh-CN': '',
    'en-US': '',
  },
  titleDesc2: {
    'zh-CN': '',
    'en-US': '',
  },
});
const formRef = ref<FormInstance>();
const { success } = useNotification();
let globalConfig: Record<string, any> = {};

const save = async () => {
  const { isSuccess } = await $fetch('/admin/v1/openatom/systemconfig/edit', {
    method: 'post',
    data: {
      configInfo: {
        ...globalConfig,
        project: {
          ...formData.value,
        },
      },
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
  }
};

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  save()
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/openatom/systemconfig/getone', {
    method: 'post',
    loadingStatus: loading,
  });

  if (isSuccess) {
    globalConfig = data.configInfo;

    formData.value = {
      pageDesc: globalConfig.project?.pageDesc ?? {},
      titleDesc1: globalConfig.project?.titleDesc1 ?? {},
      titleDesc2: globalConfig.project?.titleDesc2 ?? {},
    };
  }
}

getDetail();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit">
        <AFormItem label="页面描述(中文)" field="pageDesc.zh-CN" :rules="[{ required: true, message: '此项必填' }, { maxLength: 500 }]">
          <ATextarea v-model="formData.pageDesc['zh-CN']" :max-length="500" allow-clear show-word-limit />
        </AFormItem>

        <AFormItem label="页面描述(英文)" field="pageDesc.en-US" :rules="[{ required: true, message: '此项必填' }, { maxLength: 500 }]">
          <ATextarea v-model="formData.pageDesc['en-US']" :max-length="500" allow-clear show-word-limit />
        </AFormItem>

        <AFormItem label="孵化期描述(中文)" field="titleDesc1.zh-CN" :rules="[{ required: true, message: '此项必填' }, { maxLength: 500 }]">
          <ATextarea v-model="formData.titleDesc1['zh-CN']" :max-length="500" allow-clear show-word-limit />
        </AFormItem>

        <AFormItem label="孵化期描述(英文)" field="titleDesc1.en-US" :rules="[{ required: true, message: '此项必填' }, { maxLength: 500 }]">
          <ATextarea v-model="formData.titleDesc1['en-US']" :max-length="500" allow-clear show-word-limit />
        </AFormItem>

        <AFormItem label="孵化筹备期描述(中文)" field="titleDesc2.zh-CN" :rules="[{ required: true, message: '此项必填' }, { maxLength: 500 }]">
          <ATextarea v-model="formData.titleDesc2['zh-CN']" :max-length="500" allow-clear show-word-limit />
        </AFormItem>

        <AFormItem label="孵化筹备期描述(英文)" field="titleDesc2.en-US" :rules="[{ required: true, message: '此项必填' }, { maxLength: 500 }]">
          <ATextarea v-model="formData.titleDesc2['en-US']" :max-length="500" allow-clear show-word-limit />
        </AFormItem>

        <ADivider />

        <AFormItem>
          <ASpace>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
