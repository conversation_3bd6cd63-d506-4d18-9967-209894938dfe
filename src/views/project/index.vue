<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import useNotification from '@/hooks/notification';
import { $fetch } from '@/utils/fetch';
import { useStatusList } from './use';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const statusList = useStatusList();

const form = ref({
  title: '',
  createdTime: [],
  status: '',
});

const search = ref();
const { success } = useNotification();

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/project/v1/listPage', {
    data: {
      title: form.value.title,
      startTime: form.value.createdTime?.[0],
      endTime: form.value.createdTime?.[1],
      status: form.value.status || undefined,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.projectVO;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/project/v1/remove', {
    data: {
      id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const getStatusText = (status: string) => {
  return statusList.value.find(item => item.projectStatusCode === status)?.projectStatusName ?? '-';
}

onActivated(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pagination="{ pageSize: 20 }" :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #search-before>
        <div class="mb-20px">
          <ARadioGroup v-model="form.status" @change="refresh()">
            <ARadio value="">
              <template #radio="{ checked }">
                <ATag :checked="checked" size="large" color="arcoblue" checkable>全部</ATag>
              </template>
            </ARadio>
            <ARadio v-for="item in statusList" :key="item.projectStatusCode" :value="item.projectStatusCode">
              <template #radio="{ checked }">
                <ATag :checked="checked" size="large" color="arcoblue" checkable>{{ item.projectStatusName }}</ATag>
              </template>
            </ARadio>
          </ARadioGroup>
        </div>
      </template>

      <template #header>
        <AFormItem label="项目名称">
          <AInput v-model="form.title" allow-clear />
        </AFormItem>
        <AFormItem label="创建时间">
          <ARangePicker v-model="form.createdTime" />
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/project/add" type="primary">新增</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="项目ID" data-index="id" :width="200" />
        <ATableColumn title="项目名称" data-index="title" tooltip ellipsis />

        <ATableColumn title="LOGO" header-cell-class="w-15%">
          <template #cell="{ record }">
            <OpenImage :src="record.cardLogo" img-class="w-full h-100px object-cover" v-if="record.cardLogo" />
          </template>
        </ATableColumn>

        <ATableColumn title="分类" :width="130">
          <template #cell="{ record }">
            {{ getStatusText(record.status) }}
          </template>
        </ATableColumn>

        <ATableColumn title="序号" data-index="sort" :width="100" />

        <ATableColumn title="创建时间" data-index="createTime" header-cell-class="w-16%" />

        <ATableColumn title="操作" :width="230">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/project/view/${record.id}`">查看</OpenRouterButton>
              <OpenRouterButton size="small" :to="`/project/edit/${record.id}`">编辑</OpenRouterButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.id)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
