import { ref, type Ref } from 'vue';
import { $fetch } from '@/utils/fetch';

const getStatusList = async (list: Ref) => {
  const { isSuccess, data } = await $fetch('/admin/system/config/v1/key', {
    data: {
      key: 'project_statusList',
    },
  });

  if (isSuccess) {
    list.value = data.projectStatusList;
  }
};

export const useStatusList = () => {
  const list = ref<any[]>([]);

  getStatusList(list);

  return list;
};
