<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { useLanguage, onLanguageRouteUpdate } from '@/hooks/i18n';
import { useCategoryList } from '../use';
import { layoutStatus } from '../constants';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const { listing: categoryList, refresh: refreshCategory } = useCategoryList();
const formData = ref({
  categoryId: '',
  certificationName: '',
  description: '',
  logo: '',
  link: '',
  frame: '',
  flow: '',
  layoutState: 2,
  orderNum: 1,
  showPartners: 1,
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();
const language = useLanguage();
let dataLanguage = language;

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id && (dataLanguage === language) ? '/admin/v1/certification/update' : '/admin/v1/certification/create', {
    data: {
      ...formData.value,
      certificationId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/certification/home/<USER>');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/certification/detail', {
    data: {
      certificationId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    dataLanguage = data.lc;

    formData.value = {
      categoryId: data.categoryId,
      certificationName: data.certificationName,
      description: data.description,
      logo: data.logo,
      link: data.link,
      frame: data.frame,
      flow: data.flow,
      layoutState: data.layoutState,
      orderNum: data.orderNum,
      showPartners: data.showPartners,
    };
  }
}

const init = () => {
  refreshCategory();

  if (props.id) {
    getDetail();
  }
}

onLanguageRouteUpdate(() => {
  init();
});

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard :show-i18n="!!props.id">
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" v-confirm>
        <AFormItem v-if="!props.id">
          <AAlert>请不要忘记输入英文内容！新建后，编辑这条信息，然后点击右上角【切换到英文内容】来输入英文内容！</AAlert>
        </AFormItem>

        <AFormItem label="认证分类" field="categoryId" :rules="[{ required: true, message: '此项必选' }]">
          <ASelect placeholder="请选择" v-model="formData.categoryId" :fallback-option="false">
            <AOption v-for="item in categoryList" :key="item.categoryId" :value="item.categoryId">{{ item.categoryName }}</AOption>
          </ASelect>
        </AFormItem>

        <AFormItem label="名称" field="certificationName" :rules="[{ required: true, message: '此项必填' }]">
          <AInput v-model="formData.certificationName" :max-length="200" show-word-limit />
        </AFormItem>

        <AFormItem label="描述" field="description" :rules="[{ required: true, message: '此项必填' }]">
          <ATextarea v-model="formData.description" :max-length="500" show-word-limit />
        </AFormItem>

        <AFormItem label="LOGO" field="logo" :rules="[{ required: true, message: '此项必传' }]" help="图片尺寸: 224x224 像素">
          <OpenUpload v-model="formData.logo" :image-aspect-ratio="224 / 224" />
        </AFormItem>

        <AFormItem label="跳转链接" field="link">
          <AInput v-model="formData.link" />
        </AFormItem>

        <AFormItem label="认证体系框架" field="frame">
          <OpenUpload v-model="formData.frame" />
        </AFormItem>

        <AFormItem label="认证流程" field="flow">
          <OpenUpload v-model="formData.flow" />
        </AFormItem>

        <AFormItem label="展示合作伙伴" field="showPartners">
          <ASwitch v-model="formData.showPartners" :checked-value="1" :unchecked-value="2" checked-text="是" unchecked-text="否" />
        </AFormItem>

        <AFormItem label="序号" field="orderNum" :rules="[{ required: true, message: '此项必填' }, { type: 'number', min: 1, message: '请输入大于等于 1 的整数' }]">
          <AInputNumber v-model="formData.orderNum" />
        </AFormItem>

        <AFormItem label="状态" field="layoutState" :rules="[{ required: true, message: '此项必选' }]">
          <ASelect placeholder="请选择" v-model="formData.layoutState">
            <AOption v-for="item in layoutStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
          </ASelect>
        </AFormItem>

        <ADivider />

        <AFormItem>
          <ASpace>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
