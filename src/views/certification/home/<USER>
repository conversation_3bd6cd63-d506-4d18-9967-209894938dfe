<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useCategoryList } from '../use';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success } = useNotification();

const { listing: categoryList, refresh: refreshCategory } = useCategoryList();
const form = ref({
  layoutState: '',
  category: '',
  certificationName: '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/v1/certification/page', {
    data: {
      certificationName: form.value.certificationName,
      categoryId: form.value.category,
      layoutState: form.value.layoutState,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/v1/certification/delete', {
    data: {
      certificationId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const changeStatus = async (status: string, id: string) => {
  const { isSuccess } = await $fetch(`/admin/v1/certification/${status}`, {
    data: {
      certificationId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    search.value.refresh();
  }
};

onActivated(() => {
  refreshCategory();
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="名称">
          <AInput v-model="form.certificationName" allow-clear />
        </AFormItem>
        <AFormItem label="分类">
          <ASelect v-model="form.category">
            <AOption value="">全部</AOption>
            <AOption v-for="item in categoryList" :key="item.categoryId" :value="item.categoryId">{{ item.categoryName }}</AOption>
          </ASelect>
        </AFormItem>
        <AFormItem label="状态">
          <ASelect v-model="form.layoutState">
            <AOption value="">全部</AOption>
            <AOption :value="1">启用</AOption>
            <AOption :value="2">禁用</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/certification/home/<USER>" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="名称" data-index="name" tooltip ellipsis />

        <ATableColumn title="LOGO" :width="150">
          <template #cell="{ record }">
            <OpenImage :src="record.logo" img-class="size-100px object-cover rounded-50%" />
          </template>
        </ATableColumn>

        <ATableColumn title="分类" data-index="categoryName" :width="280" />
        <ATableColumn title="序号" data-index="orderNum" :width="200" />

        <ATableColumn title="状态" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.layoutState == 1" color="blue">启用</ATag>
            <ATag v-else-if="record.layoutState == 2">禁用</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="操作" :width="230">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/certification/home/<USER>/${record.certificationId}`">编辑</OpenRouterButton>
              <AButton type="primary" size="small" @click="changeStatus('show', record.certificationId)" v-confirm v-if="record.layoutState == 2">启用</AButton>
              <AButton status="danger" size="small" @click="changeStatus('hide', record.certificationId)" v-confirm v-if="record.layoutState == 1">禁用</AButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.certificationId)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
