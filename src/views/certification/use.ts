import { ref, type Ref } from 'vue';
import { $fetch } from '@/utils/fetch';

const getCategoryList = async (list: Ref) => {
  const { isSuccess, data } = await $fetch('/admin/v1/certification/category/list');

  if (isSuccess) {
    list.value = data;
  }
};

export const useCategoryList = () => {
  const listing = ref<any[]>([]);
  const refresh = () => getCategoryList(listing);

  return { listing, refresh };
};
