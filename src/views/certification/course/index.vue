<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success } = useNotification();
const certificationListing = ref([]);
const form = ref({
  certificationData: '',
  layoutState: '',
  courseName: '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/v1/certification/course/page', {
    data: {
      categoryId: form.value.certificationData[0],
      certificationId: form.value.certificationData[1],
      courseName: form.value.courseName,
      layoutState: form.value.layoutState,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/v1/certification/course/delete', {
    data: {
      courseId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const changeStatus = async (status: string, id: string) => {
  const { isSuccess } = await $fetch(`/admin/v1/certification/course/${status}`, {
    data: {
      courseId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    search.value.refresh();
  }
};

const getCertificationListing = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/certification/selector');

  if (isSuccess) {
    certificationListing.value = data;
  }
};

onActivated(() => {
  getCertificationListing();
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="名称">
          <AInput v-model="form.courseName" allow-clear />
        </AFormItem>
        <AFormItem label="所属认证">
          <ACascader v-model="form.certificationData" :options="certificationListing" placeholder="全部" allow-clear allow-search path-mode check-strictly />
        </AFormItem>
        <AFormItem label="状态">
          <ASelect v-model="form.layoutState">
            <AOption value="">全部</AOption>
            <AOption :value="1">启用</AOption>
            <AOption :value="2">禁用</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/certification/course/add" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="名称" data-index="courseName" tooltip ellipsis />

        <ATableColumn title="所属认证">
          <template #cell="{ record }">
            {{ record.categoryName }} / {{ record.certificationName }}
          </template>
        </ATableColumn>

        <ATableColumn title="序号" data-index="orderNum" :width="200" />

        <ATableColumn title="状态" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.layoutState == 1" color="blue">启用</ATag>
            <ATag v-else-if="record.layoutState == 2">禁用</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="操作" :width="240">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/certification/course/edit/${record.courseId}`">编辑</OpenRouterButton>
              <AButton type="primary" size="small" @click="changeStatus('show', record.courseId)" v-confirm v-if="record.layoutState == 2">启用</AButton>
              <AButton status="danger" size="small" @click="changeStatus('hide', record.courseId)" v-confirm v-if="record.layoutState == 1">禁用</AButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.courseId)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
