<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { useLanguage, onLanguageRouteUpdate } from '@/hooks/i18n';
import MarkdownEditor from '@/components/markdown-editor/index.vue';
import { layoutStatus } from '../constants';
import { marked } from 'marked';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref<Record<string, any>>({
  categoryId: '',
  certificationId: '',
  certificationData: [],
  courseName: '',
  description: '',
  link: '',
  orderNum: 1,
  layoutState: 2,
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();
const language = useLanguage();
const certificationListing = ref([]);
const editor = ref();
let dataLanguage = language;

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const data: Record<string, any> = {
    ...formData.value,
    courseId: props.id,
    categoryId: formData.value.certificationData[0],
    certificationId: formData.value.certificationData[1],
  };

  delete data.certificationData;

  const { isSuccess } = await $fetch(props.id && (dataLanguage === language) ? '/admin/v1/certification/course/update' : '/admin/v1/certification/course/create', {
    data,
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/certification/course/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/certification/course/detail', {
    data: {
      courseId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    dataLanguage = data.lc;

    formData.value = {
      certificationData: [data.categoryId, data.certificationId],
      courseName: data.courseName,
      description: data.description,
      link: data.link,
      orderNum: data.orderNum,
      layoutState: data.layoutState,
    };

    editor.value?.scrollToTop();
  }
}

const markdownValidator = (value: string, callback: (error?: string) => void) => {
  const introText = (marked.parse(value || '') as string).replace(/<[^>]+>/g, ''); // 去除 HTML 标签
  if (introText.length > 500) {
    callback('不能超过 500 个字');
  }
  else {
    callback();
  }
}

const getCertificationListing = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/certification/selector');

  if (isSuccess) {
    certificationListing.value = data;
  }
};

getCertificationListing();

const init = () => {
  if (props.id) {
    getDetail();
  }
}

onLanguageRouteUpdate(() => {
  init();
});

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard :show-i18n="!!props.id">
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" v-confirm>
        <AFormItem v-if="!props.id">
          <AAlert>请不要忘记输入英文内容！新建后，编辑这条信息，然后点击右上角【切换到英文内容】来输入英文内容！</AAlert>
        </AFormItem>

        <AFormItem label="所属认证" field="certificationData" :rules="[{ required: true, message: '此项必选' }]">
          <ACascader v-model="formData.certificationData" :options="certificationListing" placeholder="请选择" allow-clear allow-search path-mode :fallback="false" />
        </AFormItem>

        <AFormItem label="名称" field="courseName" :rules="[{ required: true, message: '此项必填' }]">
          <AInput v-model="formData.courseName" :max-length="200" allow-clear show-word-limit />
        </AFormItem>

        <AFormItem label="描述" field="description" :rules="[{ required: true, message: '此项必填' }, { validator: markdownValidator }]" help="Markdown 格式">
          <MarkdownEditor :height="200" v-model="formData.description" :toolbar="[['bold', 'italic', 'strike', 'link']]" break ref="editor" />
        </AFormItem>

        <AFormItem label="跳转链接" field="link">
          <AInput v-model="formData.link" />
        </AFormItem>

        <AFormItem label="序号" field="orderNum" :rules="[{ required: true, message: '此项必填' }, { type: 'number', min: 1, message: '请输入大于等于 1 的整数' }]">
          <AInputNumber v-model="formData.orderNum" />
        </AFormItem>

        <AFormItem label="状态" field="layoutState" :rules="[{ required: true, message: '此项必选' }]">
          <ASelect placeholder="请选择" v-model="formData.layoutState">
            <AOption v-for="item in layoutStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
          </ASelect>
        </AFormItem>

        <ADivider />

        <AFormItem>
          <ASpace>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
