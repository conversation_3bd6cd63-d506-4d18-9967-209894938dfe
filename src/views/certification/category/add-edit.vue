<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { layoutStatus } from '../constants';
import { useLanguage, onLanguageRouteUpdate } from '@/hooks/i18n';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref({
  categoryName: '',
  description: '',
  orderNum: 1,
  layoutState: 2,
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();
const language = useLanguage();
let dataLanguage = language;

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id && (dataLanguage === language) ? '/admin/v1/certification/category/update' : '/admin/v1/certification/category/create', {
    data: {
      ...formData.value,
      categoryId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/certification/category/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/certification/category/detail', {
    data: {
      categoryId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    dataLanguage = data.lc;

    formData.value = {
      categoryName: data.categoryName,
      description: data.description,
      layoutState: data.layoutState,
      orderNum: data.orderNum,
    };
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

onLanguageRouteUpdate(() => {
  init();
});

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard :show-i18n="!!props.id">
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" v-confirm>
        <AFormItem v-if="!props.id">
          <AAlert>请不要忘记输入英文内容！新建后，编辑这条信息，然后点击右上角【切换到英文内容】来输入英文内容！</AAlert>
        </AFormItem>

        <AFormItem label="名称" field="categoryName" :rules="[{ required: true, message: '此项必填' }]">
          <AInput v-model="formData.categoryName" :max-length="200" show-word-limit />
        </AFormItem>

        <AFormItem label="描述" field="description">
          <ATextarea v-model="formData.description" :max-length="500" show-word-limit />
        </AFormItem>

        <AFormItem label="序号" field="orderNum" :rules="[{ required: true, message: '此项必填' }, { type: 'number', min: 1, message: '请输入大于等于 1 的整数' }]">
          <AInputNumber v-model="formData.orderNum" />
        </AFormItem>

        <AFormItem label="状态" field="layoutState" :rules="[{ required: true, message: '此项必选' }]">
          <ASelect placeholder="请选择" v-model="formData.layoutState">
            <AOption v-for="item in layoutStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
          </ASelect>
        </AFormItem>

        <ADivider />

        <AFormItem>
          <ASpace>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
