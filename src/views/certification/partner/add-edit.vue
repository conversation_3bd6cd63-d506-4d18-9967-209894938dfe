<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { useLanguage, onLanguageRouteUpdate } from '@/hooks/i18n';
import { layoutStatus } from '../constants';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref<Record<string, any>>({
  categoryId: '',
  certificationId: '',
  certificationData: [],
  partnerName: '',
  logo: '',
  orderNum: 1,
  layoutState: 2,
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();
const language = useLanguage();
const certificationListing = ref([]);
let dataLanguage = language;

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const data: Record<string, any> = {
    ...formData.value,
    partnerId: props.id,
    categoryId: formData.value.certificationData[0],
    certificationId: formData.value.certificationData[1],
  };

  delete data.certificationData;

  const { isSuccess } = await $fetch(props.id && (dataLanguage === language) ? '/admin/v1/certification/partner/update' : '/admin/v1/certification/partner/create', {
    data,
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/certification/partner/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/certification/partner/detail', {
    data: {
      partnerId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    dataLanguage = data.lc;

    formData.value = {
      certificationData: [data.categoryId, data.certificationId],
      partnerName: data.partnerName,
      logo: data.logo,
      orderNum: data.orderNum,
      layoutState: data.layoutState,
    };
  }
}

const getCertificationListing = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/certification/selector');

  if (isSuccess) {
    certificationListing.value = data;
  }
};

getCertificationListing();

const init = () => {
  if (props.id) {
    getDetail();
  }
}

onLanguageRouteUpdate(() => {
  init();
});

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard :show-i18n="!!props.id">
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" v-confirm>
        <AFormItem v-if="!props.id">
          <AAlert>请不要忘记输入英文内容！新建后，编辑这条信息，然后点击右上角【切换到英文内容】来输入英文内容！</AAlert>
        </AFormItem>

        <AFormItem label="所属认证" field="certificationData" :rules="[{ required: true, message: '此项必选' }]">
          <ACascader v-model="formData.certificationData" :options="certificationListing" placeholder="请选择" allow-clear allow-search path-mode :fallback="false" />
        </AFormItem>

        <AFormItem label="名称" field="partnerName" :rules="[{ required: true, message: '此项必填' }]">
          <AInput v-model="formData.partnerName" :max-length="200" allow-clear show-word-limit />
        </AFormItem>

        <AFormItem label="LOGO" field="logo" :rules="[{ required: true, message: '此项必传' }]">
          <OpenUpload v-model="formData.logo" />
        </AFormItem>

        <AFormItem label="序号" field="orderNum" :rules="[{ required: true, message: '此项必填' }, { type: 'number', min: 1, message: '请输入大于等于 1 的整数' }]">
          <AInputNumber v-model="formData.orderNum" />
        </AFormItem>

        <AFormItem label="状态" field="layoutState" :rules="[{ required: true, message: '此项必选' }]">
          <ASelect placeholder="请选择" v-model="formData.layoutState">
            <AOption v-for="item in layoutStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
          </ASelect>
        </AFormItem>

        <ADivider />

        <AFormItem>
          <ASpace>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
