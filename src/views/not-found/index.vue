<script lang="ts" setup>
import { useRouter } from 'vue-router';

const router = useRouter();
const back = () => {
  // warning： Go to the node that has the permission
  router.push({ path: '/' });
};
</script>

<template>
  <div class="content">
    <AResult class="result" status="404" subtitle="页面未找到" />
    <div class="operation-row">
      <AButton key="back" type="primary" @click="back">回到首页</AButton>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.content {
  // padding-top: 100px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -121px;
  margin-left: -95px;
  text-align: center;
}
</style>
