<script lang="ts" setup>
import { ref, onMounted, inject } from 'vue';
import { AsyncDialogProvide } from '@/components/dialog';

const dialogClickButton = (button: string) => {
  console.log(button);

  return true;
}

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});

const asyncDialog = inject(AsyncDialogProvide);
const show = ref(false);

onMounted(() => {
  setTimeout(() => {
    show.value = true;
    asyncDialog?.setTitle('我是新标题');
    asyncDialog?.setButton('ok', '我是个按钮');
    asyncDialog?.autoAdjustPosition();
  }, 2000);
});

defineExpose({
  dialogClickButton,
  dialogOptions: {
    width: 1200,
  },
});
</script>

<template>
  <h1>我是一个测试！！！ {{ props.title }}</h1>
  <h1>我是一个测试！！！</h1>
  <h1>我是一个测试！！！</h1>
  <AForm :model="{}">
    <AFormItem>
      <ADatePicker show-time />
    </AFormItem>
  </AForm>
  <h1>我是一个测试！！！</h1>
  <div v-if="show">
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
    <h1>我是一个测试！！！</h1>
  </div>
</template>
