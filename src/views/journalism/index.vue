<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useCategoryList } from './use';
// import { dialog } from '@/components/dialog';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const base = import.meta.env.VITE_OPENATOM_BASE;
const { success } = useNotification();

const categoryList = useCategoryList();
const form = ref({
  releaseTime: [],
  status: '',
  category: '',
  title: '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/v1/news/page', {
    data: {
      title: form.value.title,
      beginTime: form.value.releaseTime?.[0],
      endTime: form.value.releaseTime?.[1],
      categoryId: form.value.category,
      newsStatus: form.value.status,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/v1/news/delete', {
    data: {
      newsId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const changeStatus = async (status: string, id: string) => {
  const { isSuccess } = await $fetch(`/admin/v1/news/${status}`, {
    data: {
      newsId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    search.value.refresh();
  }
};

onActivated(() => {
  search.value.refresh('reset');
});

// const clickHandler = async (name: string) => {
//   const { button } = await dialog(import('./test-window.vue'), {
//     title: '我是一个测试对话框',
//     // hideClose: true,
//     width: 800,
//     buttons: { ok: '确认' },
//     props: {
//       title: name,
//     }
//   });

//   console.log(button);
// }
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #search-before>
        <div class="mb-20px">
          <ARadioGroup v-model="form.category" @change="refresh()">
            <ARadio value="">
              <template #radio="{ checked }">
                <ATag :checked="checked" size="large" color="arcoblue" checkable>全部</ATag>
              </template>
            </ARadio>
            <ARadio v-for="item in categoryList" :key="item.categoryId" :value="item.categoryId">
              <template #radio="{ checked }">
                <ATag :checked="checked" size="large" color="arcoblue" checkable>{{ item.categoryName }}</ATag>
              </template>
            </ARadio>
          </ARadioGroup>
        </div>
      </template>

      <template #header>
        <AFormItem label="标题">
          <AInput v-model="form.title" allow-clear />
        </AFormItem>
        <AFormItem label="发布时间">
          <ARangePicker show-time v-model="form.releaseTime" />
        </AFormItem>
        <AFormItem label="状态">
          <ASelect v-model="form.status">
            <AOption :value="''">全部</AOption>
            <AOption :value="8">已发布</AOption>
            <AOption :value="9">已下线</AOption>
            <AOption :value="1">草稿</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/journalism/add" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="ID" data-index="newsId" :width="150" />
        <ATableColumn title="标题" data-index="title" tooltip ellipsis />

        <ATableColumn title="封面" header-cell-class="w-10%">
          <template #cell="{ record }">
            <OpenImage :src="record.cover" img-class="w-full h-100px object-cover" />
          </template>
        </ATableColumn>

        <ATableColumn title="分类" data-index="categoryName" :width="180" />
        <ATableColumn title="发布时间" data-index="releaseTime" header-cell-class="w-10%" />

        <ATableColumn title="状态" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.newsStatus == 8" color="blue">已发布</ATag>
            <ATag v-else-if="record.newsStatus == 9" color="red">已下线</ATag>
            <ATag v-else-if="record.newsStatus == 1">草稿</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="操作" header-cell-class="w-14%">
          <template #cell="{ record }">
            <div class="button-actions">
              <AButton size="small" :href="`${base}/journalism/detail/${record.newsId}`" target="_blank">预览</AButton>
              <OpenRouterButton size="small" :to="`/journalism/edit/${record.newsId}`">编辑</OpenRouterButton>
              <AButton type="primary" size="small" @click="changeStatus('publish', record.newsId)" v-confirm v-if="record.newsStatus == 1 || record.newsStatus == 9">发布</AButton>
              <AButton status="danger" size="small" @click="changeStatus('offline', record.newsId)" v-confirm v-if="record.newsStatus == 8">下线</AButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.newsId)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
