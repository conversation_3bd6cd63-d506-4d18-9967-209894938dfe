import { ref, type Ref } from 'vue';
import { $fetch } from '@/utils/fetch';

const getCategoryList = async (list: Ref) => {
  const { isSuccess, data } = await $fetch('/admin/v1/news/category/list', {
    data: {
      categoryStatus: 1,
    },
  });

  if (isSuccess) {
    list.value = data;
  }
};

export const useCategoryList = () => {
  const list = ref<any[]>([]);

  getCategoryList(list);

  return list;
};
