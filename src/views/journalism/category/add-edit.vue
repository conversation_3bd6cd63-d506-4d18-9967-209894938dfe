<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { useCategoryStatus, useLayoutStatus } from './use';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref({
  categoryId: '',
  categoryName: '',
  orderNum: 1,
  categoryStatus: '',
  layoutState: '',
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();
const categoryStatus = useCategoryStatus();
const layoutStatus = useLayoutStatus();

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id ? '/admin/v1/news/category/update' : '/admin/v1/news/category/create', {
    data: {
      newsId: props.id,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/journalism/category/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/news/category/info', {
    data: {
      categoryId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      categoryId: data.categoryId,
      categoryName: data.categoryName,
      categoryStatus: data.categoryStatus,
      layoutState: data.layoutState,
      orderNum: data.orderNum,
    };
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" v-confirm>
        <AFormItem label="名称" field="categoryName" :rules="[{ required: true, message: '请输入标题' }]">
          <AInput v-model="formData.categoryName" />
        </AFormItem>

        <AFormItem label="序号" field="orderNum" :rules="[{ required: true, message: '请输入序号' }]">
          <AInputNumber v-model="formData.orderNum" />
        </AFormItem>

        <template v-if="props.id">
          <AFormItem label="分类状态" field="categoryStatus" :rules="[{ required: true, message: '请选择分类状态' }]">
            <ASelect placeholder="请选择" v-model="formData.categoryStatus">
              <AOption v-for="item in categoryStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
            </ASelect>
          </AFormItem>

          <AFormItem label="展示状态" field="layoutState" :rules="[{ required: true, message: '请选择展示状态' }]">
            <ASelect placeholder="请选择" v-model="formData.layoutState">
              <AOption v-for="item in layoutStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
            </ASelect>
          </AFormItem>
        </template>

        <ADivider />

        <AFormItem>
          <ASpace>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
