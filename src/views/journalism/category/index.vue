<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useCategoryStatus, useLayoutStatus } from './use';

const listing = ref([]);
const loading = ref(false);
const search = ref();
const { success } = useNotification();

const categoryStatus = useCategoryStatus();
const layoutStatus = useLayoutStatus();
const form = ref({
  categoryName: '',
  categoryStatus: '',
  layoutState: '',
});

const refresh = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/news/category/list', {
    data: {
      categoryName: form.value.categoryName,
      categoryStatus: form.value.categoryStatus,
      layoutState: form.value.layoutState,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data;
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/v1/news/category/delete', {
    data: {
      categoryId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

onActivated(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="名称">
          <AInput v-model="form.categoryName" allow-clear />
        </AFormItem>
        <AFormItem label="分类状态">
          <ASelect v-model="form.categoryStatus">
            <AOption value="">全部</AOption>
            <AOption v-for="item in categoryStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
          </ASelect>
        </AFormItem>
        <AFormItem label="展示状态">
          <ASelect v-model="form.layoutState">
            <AOption value="">全部</AOption>
            <AOption v-for="item in layoutStatus" :key="item.value" :value="item.value">{{ item.text }}</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/journalism/category/add" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="ID" data-index="categoryId" />
        <ATableColumn title="名称" data-index="categoryName" />
        <ATableColumn title="序号" data-index="orderNum" />

        <ATableColumn title="分类状态" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.categoryStatus == 1" color="blue">启用</ATag>
            <ATag v-else-if="record.categoryStatus == 2">禁用</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="展示状态" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.layoutState == 1" color="blue">展示</ATag>
            <ATag v-else-if="record.layoutState == 2">不展示</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="操作" :width="160">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/journalism/category/edit/${record.categoryId}`">编辑</OpenRouterButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.categoryId)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
