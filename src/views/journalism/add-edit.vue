<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { useCategoryList } from './use';
import RichEditor from '@/components/rich-editor/index.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const categoryList = useCategoryList();
const formData = ref({
  categoryId: '',
  title: '',
  intro: '',
  releaseTime: '',
  cover: '',
  // preview: '',
  content: '',
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id ? '/admin/v1/news/update' : '/admin/v1/news/create', {
    data: {
      newsId: props.id,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/journalism');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/news/detail', {
    data: {
      newsId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      categoryId: data.categoryId,
      title: data.title,
      intro: data.intro,
      releaseTime: data.releaseTime,
      cover: data.cover,
      // preview: data.preview,
      content: data.content,
    };
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" v-confirm>
        <AFormItem v-if="props.id">
          <AAlert>请注意，修改新闻会下线已发布的新闻，修改完成后请重新发布这条新闻。</AAlert>
        </AFormItem>

        <AFormItem label="分类" field="categoryId" :rules="[{ required: true, message: '请选择分类' }]">
          <ASelect placeholder="请选择" v-model="formData.categoryId">
            <AOption v-for="item in categoryList" :key="item.categoryId" :value="item.categoryId">{{ item.categoryName }}</AOption>
          </ASelect>
        </AFormItem>

        <AFormItem label="标题" field="title" :rules="[{ required: true, message: '请输入标题' }, { maxLength: 150 }]">
          <AInput v-model.trim="formData.title" :max-length="150" allow-clear show-word-limit />
        </AFormItem>

        <AFormItem label="内容描述" field="intro" :rules="[{ required: true, message: '请输入内容描述' }, { maxLength: 400 }]">
          <ATextarea v-model.trim="formData.intro" :max-length="400" allow-clear show-word-limit />
        </AFormItem>

        <AFormItem label="发布时间" field="releaseTime" :rules="[{ required: true, message: '请选择发布时间' }]">
          <ADatePicker v-model="formData.releaseTime" show-time format="YYYY-MM-DD HH:mm:ss" />
        </AFormItem>

        <AFormItem label="封面图" field="cover" :rules="[{ required: true, message: '请上传封面图' }]" help="图片尺寸: 864x416 像素">
          <OpenUpload v-model="formData.cover" :image-aspect-ratio="864 / 416" />
        </AFormItem>

        <!-- <AFormItem label="缩略图" field="preview" :rules="[{ required: true, message: '请上传缩略图' }]" help="图片尺寸: 272x152 像素">
          <OpenUpload v-model="formData.preview" />
        </AFormItem> -->

        <AFormItem label="正文" field="content" :rules="[{ required: true, message: '请输入正文' }]">
          <RichEditor :height="800" v-model="formData.content" />
        </AFormItem>

        <ADivider />

        <AFormItem>
          <ASpace>
            <!-- <OpenRouterButton to="/journalism">返回列表</OpenRouterButton> -->
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
