<script lang="ts" setup>
import Banner from './components/banner.vue';
import DataPanel from './components/data-panel.vue';
import ContentChart from './components/content-chart.vue';
import PopularContent from './components/popular-content.vue';
import CategoriesPercent from './components/categories-percent.vue';
// import QuickOperation from './components/quick-operation.vue';
</script>

<template>
  <OpenPageSection class="page-welcome">
    <div class="panel">
      <Banner />
      <DataPanel />
      <ContentChart />
    </div>

    <AGrid :cols="24" :col-gap="16" :row-gap="16" class="mt-16px">
      <AGridItem :span="{ xs: 24, sm: 24, md: 24, lg: 24, xl: 24, xxl: 12 }">
        <PopularContent />
      </AGridItem>
      <AGridItem :span="{ xs: 24, sm: 24, md: 24, lg: 24, xl: 24, xxl: 12 }">
        <CategoriesPercent />
      </AGridItem>
    </AGrid>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
.page-welcome {
  // display: flex;
  padding-top: 20px;
  background-color: var(--color-fill-2);

  // .left-side {
  //   flex: 1;
  //   overflow: auto;
  // }

  // .right-side {
  //   width: 280px;
  //   margin-left: 16px;
  // }

  .panel {
    overflow: auto;
    background-color: var(--color-bg-2);
    border-radius: 4px;
  }

  :deep(.panel-border) {
    margin-bottom: 0;
    border-bottom: 1px solid rgb(var(--gray-2));
  }

  .moduler-wrap {
    background-color: var(--color-bg-2);
    border-radius: 4px;

    :deep(.text) {
      color: rgb(var(--gray-8));
      font-size: 12px;
      text-align: center;
    }

    :deep(.wrapper) {
      margin-bottom: 8px;
      text-align: center;
      cursor: pointer;

      // &:last-child {
      //   .text {
      //     margin-bottom: 0;
      //   }
      // }

      &:hover {
        .icon {
          color: rgb(var(--arcoblue-6));
          background-color: #e8f3ff;
        }

        .text {
          color: rgb(var(--arcoblue-6));
        }
      }
    }

    :deep(.icon) {
      display: inline-block;
      width: 32px;
      height: 32px;
      margin-bottom: 4px;
      color: rgb(var(--dark-gray-1));
      font-size: 16px;
      line-height: 32px;
      text-align: center;
      background-color: rgb(var(--gray-1));
      border-radius: 4px;
    }
  }
  // @media (min-width: 1250px) and (max-width: 1450px) {
  //   .right-side {
  //     display: none;
  //   }
  // }
}
</style>
