<script lang="ts" setup>
import type {
  TooltipComponentOption,
  GridComponentOption,
  GraphicComponentOption,
  LineSeriesOption,
  ComposeOption,
  DefaultLabelFormatterCallbackParams,
} from 'echarts';

import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import { TooltipComponent, GridComponent, GraphicComponent } from 'echarts/components';
import { SVGRenderer } from 'echarts/renderers';
import { UniversalTransition } from 'echarts/features';
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { $fetch } from '@/utils/fetch';
import { addDays, format, parse, differenceInCalendarDays, isToday } from 'date-fns';

// 通过 ComposeOption 来组合出一个只有必须组件和图表的 Option 类型
type ECOption = ComposeOption<
  | LineSeriesOption
  | TooltipComponentOption
  | GridComponentOption
  | GraphicComponentOption
>;

// 注册必须的组件
echarts.use([
  TooltipComponent,
  GridComponent,
  GraphicComponent,
  LineChart,
  SVGRenderer,
  UniversalTransition,
]);

export interface AnyObject {
  [key: string]: unknown;
}

export interface ToolTipFormatterCallbackParams extends DefaultLabelFormatterCallbackParams {
  axisDim: string;
  axisIndex: number;
  axisType: string;
  axisId: string;
  axisValue: string;
  axisValueLabel: string;
}

const chartEl = ref();
const loading = ref(false);
const xAxis = ref<string[]>([]);
const chartsData = ref<number[]>([]);

const graphicFactory = (side: AnyObject) => {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
};

const graphicElements = ref([
  graphicFactory({ left: '2.6%' }),
  graphicFactory({ right: 0 }),
]);

const option: ECOption = {
  grid: {
    left: '2.6%',
    right: '0',
    top: '10',
    bottom: '30',
  },
  xAxis: {
    type: 'category',
    offset: 2,
    boundaryGap: false,
    axisLabel: {
      color: '#4E5969',
      rich: {
        today: {
          color: '#2d2d2d',
          fontWeight: 'bold',
        },
      },
    },
    axisLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: '#E5E8EF',
      },
    },
    axisPointer: {
      show: true,
      lineStyle: {
        color: '#23ADFF',
        width: 2,
      },
    },
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false,
    },
    minInterval: 1,
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: '#E5E8EF',
      },
    },
  },
  tooltip: {
    trigger: 'axis',
    formatter(params) {
      const [firstElement] = params as ToolTipFormatterCallbackParams[];
      return `<div>
<p class="tooltip-title">${firstElement.axisValueLabel}${isToday(parse(firstElement.axisValueLabel, 'yyyy-MM-dd', new Date())) ? '（今天）' : ''}</p>
<div class="content-panel"><span>活动数量</span><span class="tooltip-value">${firstElement.value}</span></div>
</div>`;
    },
    className: 'echarts-tooltip-diy',
  },
  graphic: {
    elements: graphicElements.value,
  },
  series: [
    {
      data: [],
      type: 'line',
      smooth: true,
      symbolSize: 12,
      emphasis: {
        focus: 'series',
        itemStyle: {
          borderWidth: 2,
        },
      },
      lineStyle: {
        width: 3,
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          {
            offset: 0,
            color: 'rgba(30, 231, 255, 1)',
          },
          {
            offset: 0.5,
            color: 'rgba(36, 154, 255, 1)',
          },
          {
            offset: 1,
            color: 'rgba(111, 66, 251, 1)',
          },
        ]),
      },
      showSymbol: false,
      areaStyle: {
        opacity: 0.8,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(17, 126, 255, 0.16)',
          },
          {
            offset: 1,
            color: 'rgba(17, 128, 255, 0)',
          },
        ]),
      },
    },
  ],
};

let chartInstance: echarts.ECharts;

const refresh = async () => {
  const now = new Date();
  const days = 30;

  const { isSuccess, data } = await $fetch('/admin/v1/activity/page', {
    data: {
      startTime: format(addDays(now, -days), 'yyyy-MM-dd 00:00:00'),
      endTime: format(addDays(now, days), 'yyyy-MM-dd 23:59:59'),
      page: 1,
      pageSize: 100,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    xAxis.value = [];
    chartsData.value = [];

    for (let i = -days; i < days + 1; i++) {
      const current = addDays(now, i);

      xAxis.value.push(format(current, 'yyyy-MM-dd'));
      chartsData.value.push(data.list.filter((item: Record<string, string>) => {
        const startTime = parse(item.startTime, 'yyyy-MM-dd HH:mm:ss', new Date());
        const endTime = parse(item.endTime, 'yyyy-MM-dd HH:mm:ss', new Date());

        // 判断是否在活动时间范围内
        if (differenceInCalendarDays(current, startTime) >= 0 && differenceInCalendarDays(current, endTime) <= 0) {
          return true;
        }
      }).length);
    }

    chartInstance.setOption({
      xAxis: {
        data: xAxis.value,
        axisLabel: {
          formatter(value: string, idx: number) {
            if (idx === 0) { return ''; }
            if (idx === xAxis.value.length - 1) { return ''; }
            return isToday(parse(value, 'yyyy-MM-dd', new Date())) ? `{today|${value}}` : value;
          },
        },
        splitLine: {
          interval: (idx: number) => {
            if (idx === 0) { return false; }
            if (idx === xAxis.value.length - 1) { return false; }
            return true;
          },
        },
      },
      series: [{
        data: chartsData.value,
      }],
    });
  }
}

onMounted(async () => {
  await nextTick();

  chartInstance = echarts.init(chartEl.value, null, { renderer: 'svg' });
  chartInstance.setOption(option);

  refresh();

  if (window.ResizeObserver) {
    const resizeObserver = new ResizeObserver(() => {
      chartInstance.resize();
    });

    resizeObserver.observe(chartEl.value);
  }
});

onUnmounted(() => {
  chartInstance?.dispose();
});
</script>

<template>
  <ASpin :loading="loading" class="content-chart w-full">
    <OpenGeneralCard title="近期活动趋势">
      <div ref="chartEl" class="h-289px w-full"></div>
    </OpenGeneralCard>
  </ASpin>
</template>

<style lang="scss" scoped>
.content-chart {
  :deep() {
    .echarts-tooltip-diy {
      background: linear-gradient(304.17deg, rgba(253, 254, 255, 0.6) -6.04%, rgba(244, 247, 252, 0.6) 85.2%) !important;
      border: none !important;
      border-radius: 6px !important;
      backdrop-filter: blur(10px) !important;

      .content-panel {
        display: flex;
        justify-content: space-between;
        width: 164px;
        height: 32px;
        margin-bottom: 4px;
        padding: 0 9px;
        line-height: 32px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 4px;
        box-shadow: 6px 0 20px rgba(34, 87, 188, 0.1);
      }

      .tooltip-title {
        margin: 0 0 10px 0;
      }

      p {
        margin: 0;
      }

      .tooltip-title,
      .tooltip-value {
        display: flex;
        align-items: center;
        color: #1d2129;
        font-weight: bold;
        font-size: 13px;
        line-height: 15px;
        text-align: right;
      }

      .tooltip-item-icon {
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-right: 8px;
        border-radius: 50%;
      }
    }
  }
}
</style>
