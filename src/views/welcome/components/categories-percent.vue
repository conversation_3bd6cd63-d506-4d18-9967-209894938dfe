<script lang="ts" setup>
import type { PieSeriesOption } from 'echarts/charts';
import type { TooltipComponentOption, GridComponentOption, GraphicComponentOption } from 'echarts/components';
import type { ComposeOption } from 'echarts/core';

import * as echarts from 'echarts/core';
import { Pie<PERSON>hart } from 'echarts/charts';
import { TooltipComponent, GridComponent, GraphicComponent, LegendComponent } from 'echarts/components';
import { SVGRenderer } from 'echarts/renderers';
import { UniversalTransition } from 'echarts/features';
import { onMounted, ref, watch, nextTick, onUnmounted } from 'vue';
import { useAppStore } from '@/store';
import { $fetch } from '@/utils/fetch';
import { groupBy } from 'lodash-es';

// 通过 ComposeOption 来组合出一个只有必须组件和图表的 Option 类型
type ECOption = ComposeOption<
  | PieSeriesOption
  | TooltipComponentOption
  | GridComponentOption
  | GraphicComponentOption
>;

// 注册必须的组件
echarts.use([
  TooltipComponent,
  GridComponent,
  GraphicComponent,
  LegendComponent,
  PieChart,
  SVGRenderer,
  UniversalTransition,
]);

const chartEl = ref();
const loading = ref(false);
const appStore = useAppStore();
let value1 = 0;
let value2 = 0;
let value3 = 0;
let total = 0;

const getOption = (isDark: boolean): ECOption => ({
  legend: {
    left: 'center',
    data: ['捐赠期', '孵化期', '其它'],
    bottom: 0,
    icon: 'circle',
    itemWidth: 8,
    textStyle: {
      color: isDark ? 'rgba(255, 255, 255, 0.7)' : '#4E5969',
    },
    itemStyle: {
      borderWidth: 0,
    },
  },
  tooltip: {
    show: true,
    trigger: 'item',
  },
  graphic: {
    elements: [
      {
        type: 'text',
        left: 'center',
        top: '40%',
        style: {
          text: '项目数',
          fill: isDark ? '#ffffffb3' : '#4E5969',
          fontSize: 14,
        },
      },
      {
        type: 'text',
        left: 'center',
        top: '50%',
        style: {
          text: String(total),
          fill: isDark ? '#ffffffb3' : '#1D2129',
          fontSize: 16,
          fontWeight: 500,
        },
      },
    ],
  },
  series: [
    {
      type: 'pie',
      radius: ['50%', '70%'],
      center: ['50%', '50%'],
      label: {
        formatter: '{d}%',
        fontSize: 14,
        color: isDark ? 'rgba(255, 255, 255, 0.7)' : '#4E5969',
      },
      itemStyle: {
        borderColor: isDark ? '#232324' : '#fff',
        borderWidth: 1,
      },
      data: [
        {
          value: [value1],
          name: '捐赠期',
          itemStyle: {
            color: isDark ? '#3D72F6' : '#249EFF',
          },
        },
        {
          value: [value2],
          name: '孵化期',
          itemStyle: {
            color: isDark ? '#A079DC' : '#313CA9',
          },
        },
        {
          value: [value3],
          name: '其它',
          itemStyle: {
            color: isDark ? '#6CAAF5' : '#21CCFF',
          },
        },
      ],
    },
  ],
});

let chartInstance: echarts.ECharts;

const refresh = async () => {
  const { isSuccess, data } = await $fetch('/admin/project/v1/listPage', {
    data: {
      page: 1,
      pageSize: 100,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    const result = groupBy(data.projectVO, 'status');

    value1 = result[1].length;
    value2 = result[2].length;
    value3 = data.totalCount - result[1].length - result[2].length;
    total = data.totalCount;

    chartInstance.setOption(getOption(appStore.theme === 'dark'));
  }
}

watch(() => appStore.theme, (value) => {
  if (chartInstance) {
    chartInstance.setOption(getOption(value === 'dark'));
  }
});

onMounted(async () => {
  await nextTick();

  chartInstance = echarts.init(chartEl.value, null, { renderer: 'svg' });
  chartInstance.setOption(getOption(appStore.theme === 'dark'));

  refresh();

  if (window.ResizeObserver) {
    const resizeObserver = new ResizeObserver(() => {
      chartInstance.resize();
    });

    resizeObserver.observe(chartEl.value);
  }
});

onUnmounted(() => {
  chartInstance?.dispose();
});
</script>

<template>
  <ASpin :loading="loading" class="w-full">
    <OpenGeneralCard title="项目分类占比">
      <div class="h-310px" ref="chartEl"></div>
    </OpenGeneralCard>
  </ASpin>
</template>

<style lang="scss" scoped>
</style>
