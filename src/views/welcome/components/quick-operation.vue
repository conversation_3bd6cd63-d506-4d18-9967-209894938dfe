<script lang="ts" setup>
import { IconFile, IconBook, IconUser } from "@arco-design/web-vue/es/icon";

const links = [
  { text: '项目管理', to: '/project/index', icon: IconFile },
  { text: '新闻管理', to: '/journalism/index', icon: IconBook },
  { text: '理事会管理', to: '/about/council/index', icon: IconUser },
  { text: 'TOC管理', to: '/about/toc/index', icon: IconUser },
];
</script>

<template>
  <OpenGeneralCard title="快捷操作" :header-style="{ paddingBottom: '0' }" :body-style="{ padding: '24px 20px 0 20px' }">
    <ARow :gutter="8">
      <RouterLink :to="link.to" custom v-slot="{ navigate }" v-for="link in links" :key="link.text">
        <ACol :key="link.text" :span="8" class="wrapper" @click="navigate">
          <div class="icon">
            <Component :is="link.icon" />
          </div>
          <ATypographyParagraph class="text">
            {{ link.text }}
          </ATypographyParagraph>
        </ACol>
      </RouterLink>
    </ARow>
  </OpenGeneralCard>
</template>

<style lang="scss" scoped>
</style>
