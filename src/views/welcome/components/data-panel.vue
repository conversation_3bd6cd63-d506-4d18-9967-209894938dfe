<script lang="ts" setup>
import { $fetch } from '@/utils/fetch';
import { ref } from 'vue';

const detail = ref({
  journalism: 0,
  project: 0,
  events: 0,
  donor: 0,
});

const getJournalismTotal = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/news/page', {
    data: {
      page: 1,
      pageSize: 1,
    },
  });

  if (isSuccess) {
    detail.value.journalism = data.totalCount;
  }
}

const getProjectTotal = async () => {
  const { isSuccess, data } = await $fetch('/admin/project/v1/listPage', {
    data: {
      page: 1,
      pageSize: 1,
    },
  });

  if (isSuccess) {
    detail.value.project = data.totalCount;
  }
}

const getDonorTotal = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/donor/page', {
    data: {
      page: 1,
      pageSize: 1,
    },
  });

  if (isSuccess) {
    detail.value.donor = data.totalCount;
  }
}

const getEventsTotal = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/activity/page', {
    data: {
      page: 1,
      pageSize: 1,
    },
  });

  if (isSuccess) {
    detail.value.events = data.totalCount;
  }
}

getJournalismTotal();
getProjectTotal();
getEventsTotal();
getDonorTotal();
</script>

<template>
  <AGrid :cols="24" :row-gap="16" class="panel">
    <AGridItem class="panel-col" :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 12, xxl: 6 }">
      <ASpace>
        <AAvatar :size="54" class="col-avatar">
          <img alt="avatar" src="@/assets/images/1.svg">
        </AAvatar>
        <AStatistic title="新闻" :value="detail.journalism" :value-from="0" animation show-group-separator>
          <template #suffix>
            条
          </template>
        </AStatistic>
      </ASpace>
    </AGridItem>
    <AGridItem class="panel-col <2xl:b-r-0!" :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 12, xxl: 6 }">
      <ASpace>
        <AAvatar :size="54" class="col-avatar">
          <img alt="avatar" src="@/assets/images/2.svg">
        </AAvatar>
        <AStatistic title="项目" :value="detail.project" :value-from="0" animation show-group-separator>
          <template #suffix>
            个
          </template>
        </AStatistic>
      </ASpace>
    </AGridItem>
    <AGridItem class="panel-col" :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 12, xxl: 6 }">
      <ASpace>
        <AAvatar :size="54" class="col-avatar">
          <img alt="avatar" src="@/assets/images/3.svg">
        </AAvatar>
        <AStatistic title="活动" :value="detail.events" :value-from="0" animation show-group-separator>
          <template #suffix>
            个
          </template>
        </AStatistic>
      </ASpace>
    </AGridItem>
    <AGridItem class="panel-col b-r-0!" :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 12, xxl: 6 }">
      <ASpace>
        <AAvatar :size="54" class="col-avatar">
          <img alt="avatar" src="@/assets/images/4.svg">
        </AAvatar>
        <AStatistic title="捐赠人" :value="detail.donor" :value-from="0" animation show-group-separator>
          <template #suffix>
            个
          </template>
        </AStatistic>
      </ASpace>
    </AGridItem>
    <AGridItem :span="24">
      <ADivider class="panel-border" />
    </AGridItem>
  </AGrid>
</template>

<style lang="scss" scoped>
.arco-grid.panel {
  margin-bottom: 0;
  padding: 16px 20px 0 20px;
}

.panel-col {
  padding-left: 43px;
  border-right: 1px solid rgb(var(--gray-2));
}

.col-avatar {
  margin-right: 12px;
  background-color: var(--color-fill-2);
}

.up-icon {
  color: rgb(var(--red-6));
}

.unit {
  margin-left: 8px;
  color: rgb(var(--gray-8));
  font-size: 12px;
}

:deep(.panel-border) {
  margin: 4px 0 0 0;
}
</style>
