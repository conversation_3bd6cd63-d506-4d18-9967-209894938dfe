<script lang="ts" setup>
import { ref } from 'vue';
import type { TableData } from '@arco-design/web-vue/es/table/interface';
import { $fetch } from '@/utils/fetch';

const loading = ref(false);
const renderList = ref<TableData[]>();
const base = import.meta.env.VITE_OPENATOM_BASE;

const refresh = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/news/page', {
    data: {
      newsStatus: 8,
      page: 1,
      pageSize: 5,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    renderList.value = data.list;
  }
}

refresh();
</script>

<template>
  <ASpin :loading="loading" style="width: 100%;">
    <OpenGeneralCard title="近期新闻" class="min-h-395px" :header-style="{ paddingBottom: '0' }" :body-style="{ padding: '17px 20px 21px 20px' }">
      <ASpace direction="vertical" :size="10" fill>
        <ATable :data="renderList" :pagination="false" :bordered="false">
          <template #columns>
            <ATableColumn title="标题" ellipsis tooltip>
              <template #cell="{ record }">
                <ALink :href="`${base}/journalism/detail/${record.newsId}`" class="inline" target="_blank">{{ record.title }}</ALink>
              </template>
            </ATableColumn>
            <ATableColumn title="分类" data-index="categoryName" :width="200" />
            <ATableColumn title="发布时间" data-index="releaseTime" :width="200" />
          </template>
        </ATable>
      </ASpace>
    </OpenGeneralCard>
  </ASpin>
</template>

<style lang="scss" scoped>
:deep(.arco-table-tr) {
  height: 44px;
}
</style>
