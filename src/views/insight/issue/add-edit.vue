<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import MarkdownEditor from '@/components/markdown-editor/index.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref({
  title: '',
  issueNumber: '',
  summary: '',
  content: '',
  intro: '',
  releaseDate: '',
  issueStatus: 1,
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();
const articles = ref<Record<string, any>[]>([]);
const articleId = ref('');
const editor = ref();

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id ? '/admin/v1/insight/issue/update' : '/admin/v1/insight/issue/create', {
    data: {
      issueId: props.id,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/insight/issue/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/insight/issue/detail', {
    data: {
      issueId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      title: data.title,
      issueNumber: data.issueNumber,
      summary: data.summary,
      content: data.content,
      intro: data.intro,
      releaseDate: data.releaseDate,
      issueStatus: data.issueStatus,
    };
  }
}

const getArticles = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/insight/issue/article/list', {
    data: {
      issueId: props.id,
    },
  });

  if (isSuccess) {
    articles.value = data ?? [];
  }
}

const init = () => {
  if (props.id) {
    getDetail();
    getArticles();
  }
}

init();

const insertArticleLink = () => {
  if (!articleId.value) {
    return;
  }

  const article: Record<string, any> | undefined = articles.value.find(item => item.articleId === articleId.value);

  if (!article) {
    return;
  }

  editor.value.insertText(`[${article.title}](/insight/article/${article.articleId})`);
}

const insertArticleLinkAll = () => {
  let text = '\n';

  articles.value.forEach(item => {
    text += `* [${item.title}](/insight/article/${item.articleId})\n`;
  });

  editor.value.insertText(text);
}
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" v-confirm>
        <AFormItem label="名称" field="title" :rules="[{ required: true, message: '此项必填' }]">
          <AInput v-model="formData.title" />
        </AFormItem>

        <AFormItem label="期数" field="issueNumber" :rules="[{ required: true, message: '此项必填' }]">
          <AInput v-model="formData.issueNumber" />
        </AFormItem>

        <AFormItem label="简介" field="intro" :rules="[{ required: true, message: '此项必填' }]">
          <ATextarea v-model="formData.intro" :max-length="200" show-word-limit />
        </AFormItem>

        <AFormItem label="发布时间" field="releaseDate" :rules="[{ required: true, message: '此项必填' }]">
          <AMonthPicker v-model="formData.releaseDate" />
        </AFormItem>

        <AFormItem label="状态" field="issueStatus" v-if="props.id">
          <ASwitch v-model="formData.issueStatus" :checked-value="8" :unchecked-value="1">
            <template #checked>
              已发布
            </template>
            <template #unchecked>
              草稿
            </template>
          </ASwitch>
        </AFormItem>

        <AFormItem label="本期看点" field="summary" help="Markdown 格式" :rules="[{ required: true, message: '此项必填' }]">
          <MarkdownEditor v-model="formData.summary" break />
        </AFormItem>

        <AFormItem label="目录内容" field="content" help="Markdown 格式，可等到添加文章后再修改">
          <template v-if="props.id">
            <ACard title="" class="w-full">
              <div class="flex gap-x-5">
                <ASelect class="w-500px" v-model="articleId" placeholder="(请选择文章)">
                  <AOption v-for="item in articles" :key="item.articleId" :value="item.articleId">
                    {{ item.title }}
                  </AOption>
                </ASelect>

                <AButton @click="insertArticleLink" :disabled="!articleId">插入文章链接</AButton>
                <AButton @click="insertArticleLinkAll">插入本期所有文章链接</AButton>
              </div>

              <ADivider />

              <MarkdownEditor v-model="formData.content" break ref="editor" />
            </ACard>
          </template>
          <template v-else>
            <MarkdownEditor v-model="formData.content" break ref="editor" />
          </template>
        </AFormItem>

        <ADivider />

        <AFormItem>
          <ASpace>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
