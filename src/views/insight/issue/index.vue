<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success } = useNotification();

const form = ref({
  title: '',
  issueNumber: '',
  issueStatus: 0,
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/v1/insight/issue/page', {
    data: {
      title: form.value.title,
      issueNumber: form.value.issueNumber,
      issueStatus: form.value.issueStatus,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/v1/insight/issue/delete', {
    data: {
      issueId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

onActivated(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="名称">
          <AInput v-model="form.title" allow-clear />
        </AFormItem>
        <AFormItem label="期数">
          <AInput v-model="form.issueNumber" allow-clear />
        </AFormItem>
        <AFormItem label="状态">
          <ASelect v-model="form.issueStatus">
            <AOption :value="0">全部</AOption>
            <AOption :value="1">草稿</AOption>
            <AOption :value="8">已发布</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/insight/issue/add" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="名称" data-index="title" />
        <ATableColumn title="期数" data-index="issueNumber" />
        <ATableColumn title="简介" data-index="intro" tooltip ellipsis />
        <ATableColumn title="发布时间" data-index="releaseDate" :width="200" />
        <ATableColumn title="创建时间" data-index="createTime" :width="200" />

        <ATableColumn title="状态" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.issueStatus == 8" color="blue">已发布</ATag>
            <ATag v-else-if="record.issueStatus == 1">草稿</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="操作" :width="180">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/insight/issue/edit/${record.issueId}`">编辑</OpenRouterButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.issueId)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
