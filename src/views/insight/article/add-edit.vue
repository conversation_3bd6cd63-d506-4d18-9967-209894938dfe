<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import MarkdownEditor from '@/components/markdown-editor/index.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref({
  issueId: '',
  title: '',
  // intro: '',
  content: '',
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();
const issues = ref<Record<string, any>[]>([]);

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id ? '/admin/v1/insight/issue/article/update' : '/admin/v1/insight/issue/article/create', {
    data: {
      articleId: props.id,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/insight/article/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/insight/issue/article/detail', {
    data: {
      articleId: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      title: data.title,
      issueId: data.issueId,
      // intro: data.intro,
      content: data.content,
    };
  }
}

const getIssues = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/insight/issue/list');

  if (isSuccess) {
    issues.value = data;
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }

  getIssues();
}

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit">
        <AFormItem label="标题" field="title" :rules="[{ required: true, message: '此项必填' }]">
          <AInput v-model="formData.title" />
        </AFormItem>

        <AFormItem label="所属期刊" field="issueId" :rules="[{ required: true, message: '此项必选' }]">
          <ASelect v-model="formData.issueId" placeholder="请选择所属期刊" :fallback-option="false">
            <AOption v-for="item in issues" :key="item.issueId" :value="item.issueId">{{ item.issueNumber }} - {{ item.title }}</AOption>
          </ASelect>
        </AFormItem>

        <AFormItem label="内容" field="content" help="Markdown 格式" :rules="[{ required: true, message: '此项必填' }]">
          <MarkdownEditor v-model="formData.content" break />
        </AFormItem>

        <ADivider />

        <AFormItem>
          <ASpace>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
