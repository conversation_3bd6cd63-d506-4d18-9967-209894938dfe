<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success } = useNotification();

const form = ref({
  title: '',
  issueId: '',
});
const issues = ref<Record<string, any>[]>([]);

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/v1/insight/issue/article/page', {
    data: {
      title: form.value.title,
      issueId: form.value.issueId,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/v1/insight/issue/article/delete', {
    data: {
      articleId: id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const getIssues = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/insight/issue/list');

  if (isSuccess) {
    issues.value = data;
  }
}

onActivated(() => {
  getIssues();
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="标题">
          <AInput v-model="form.title" allow-clear />
        </AFormItem>
        <AFormItem label="所属期刊">
          <ASelect v-model="form.issueId" class="w-500px">
            <AOption value="">全部</AOption>
            <AOption v-for="item in issues" :key="item.issueId" :value="item.issueId">{{ item.title }} - {{ item.issueNumber }}</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/insight/article/add" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="标题" data-index="title" />

        <ATableColumn title="所属期刊">
          <template #cell="{ record }">
            {{ record.issueNumber }} - {{ record.issueTitle }}
          </template>
        </ATableColumn>

        <ATableColumn title="创建时间" data-index="createTime" :width="200" />

        <ATableColumn title="操作" :width="180">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/insight/article/edit/${record.articleId}`">编辑</OpenRouterButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.articleId)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
