<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { useCategoryList } from './use';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const categoryList = useCategoryList();
const formData = ref({
  informationType: '',
  title: '',
  fileUrl: '',
  displayOrder: 1,
  status: 2,
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id ? '/admin/v1/public/information/update' : '/admin/v1/public/information/create', {
    data: {
      id: props.id,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/information/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/public/information/detail', {
    data: {
      id: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      informationType: data.informationType,
      title: data.title,
      fileUrl: data.fileUrl,
      displayOrder: data.displayOrder,
      status: data.status,
    };
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" v-confirm>
        <AFormItem label="分类" field="informationType" :rules="[{ required: true, message: '请选择分类' }]">
          <ASelect placeholder="请选择" v-model="formData.informationType">
            <AOption v-for="item in categoryList" :key="item.value" :value="item.value">{{ item.text }}</AOption>
          </ASelect>
        </AFormItem>

        <AFormItem label="标题" field="title" :rules="[{ required: true, message: '请输入标题' }]">
          <AInput v-model="formData.title" />
        </AFormItem>

        <AFormItem label="文档" field="fileUrl" :rules="[{ required: true, message: '请上传文档' }]" help="文档格式: PDF">
          <OpenUpload v-model="formData.fileUrl" accept=".pdf" list-type="text" :max-file-size="1000 * 1000 * 50" />
        </AFormItem>

        <AFormItem label="序号" field="displayOrder" :rules="[{ required: true, message: '请输入序号' }, { type: 'number', min: 1, message: '请输入合法序号' }]" help="数字越小排序越靠前，最小为 1">
          <AInputNumber v-model="formData.displayOrder" :precision="0" :min="1" />
        </AFormItem>

        <AFormItem label="对外展示" field="status" help="发布后将在官网展示，下线后将不在官网展示">
          <ARadioGroup v-model="formData.status">
            <ARadio :value="1">发布</ARadio>
            <ARadio :value="2">下线</ARadio>
          </ARadioGroup>
        </AFormItem>

        <ADivider />

        <AFormItem>
          <ASpace>
            <OpenRouterButton to="/information/index">返回列表</OpenRouterButton>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
