<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useCategoryList } from './use';
// import { dialog } from '@/components';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success } = useNotification();

const categoryList = useCategoryList();
const form = ref({
  time: [],
  category: 0,
  title: '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/v1/public/information/page', {
    data: {
      title: form.value.title,
      beginTime: form.value.time?.[0],
      endTime: form.value.time?.[1],
      informationType: form.value.category,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/v1/public/information/delete', {
    data: {
      id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const changeStatus = async (status: string, id: string) => {
  const { isSuccess } = await $fetch(`/admin/v1/public/information/${status}`, {
    data: {
      id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    search.value.refresh();
  }
};

onActivated(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="分类">
          <ASelect v-model="form.category">
            <AOption :value="0">全部</AOption>
            <AOption v-for="item in categoryList" :key="item.value" :value="item.value">{{ item.text }}</AOption>
          </ASelect>
        </AFormItem>
        <AFormItem label="标题">
          <AInput v-model="form.title" allow-clear />
        </AFormItem>
        <AFormItem label="创建时间">
          <ARangePicker show-time v-model="form.time" />
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/information/add" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="标题" data-index="title" />

        <ATableColumn title="分类" :width="200">
          <template #cell="{ record }">
            {{ categoryList.find((item) => item.value === record.informationType)?.text }}
          </template>
        </ATableColumn>

        <ATableColumn title="序号" data-index="displayOrder" :width="100" />
        <ATableColumn title="创建时间" data-index="createTime" :width="200" />

        <ATableColumn title="操作" :width="340">
          <template #cell="{ record }">
            <div class="button-actions">
              <AButton size="small" :href="record.fileUrl" target="_blank">查看文档</AButton>
              <OpenRouterButton size="small" :to="`/information/edit/${record.id}`">编辑</OpenRouterButton>
              <AButton type="primary" size="small" @click="changeStatus('publish', record.id)" v-confirm v-if="record.status == 2">发布</AButton>
              <AButton status="danger" size="small" @click="changeStatus('offline', record.id)" v-confirm v-if="record.status == 1">下线</AButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.id)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
