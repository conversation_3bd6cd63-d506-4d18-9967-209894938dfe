<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import MarkdownEditor from '@/components/markdown-editor/index.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref({
  title: '',
  summary: '',
  fileUrl: '',
  displayOrder: 1,
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id ? '/admin/v1/insight/update' : '/admin/v1/insight/create', {
    data: {
      id: props.id,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/information/insight/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/v1/insight/detail', {
    data: {
      id: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      title: data.title,
      summary: data.summary,
      fileUrl: data.fileUrl,
      displayOrder: data.displayOrder,
    };
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit" v-confirm>
        <AFormItem label="标题" field="title" :rules="[{ required: true, message: '请输入标题' }]">
          <AInput v-model="formData.title" />
        </AFormItem>

        <AFormItem label="本期看点" field="summary" :rules="[{ required: true, message: '请输入本期看点' }]">
          <MarkdownEditor v-model="formData.summary" />
        </AFormItem>

        <AFormItem label="文档" field="fileUrl" :rules="[{ required: true, message: '请上传文档' }]" help="文档格式: PDF">
          <OpenUpload v-model="formData.fileUrl" accept=".pdf" list-type="text" :max-file-size="1000 * 1000 * 60" />
        </AFormItem>

        <AFormItem label="序号" field="displayOrder" :rules="[{ required: true, message: '请输入序号' }, { type: 'number', min: 1, message: '请输入合法序号' }]" help="数字越小排序越靠前，最小为 1">
          <AInputNumber v-model="formData.displayOrder" :precision="0" :min="1" />
        </AFormItem>

        <ADivider />

        <AFormItem>
          <ASpace>
            <OpenRouterButton to="/information/insight/index">返回列表</OpenRouterButton>
            <AButton html-type="submit" type="primary" :loading="loading">提交</AButton>
          </ASpace>
        </AFormItem>
      </AForm>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
