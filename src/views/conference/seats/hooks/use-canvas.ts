/**
 * 画布操作相关的组合式函数
 */

import { computed, type Ref, type ShallowRef } from 'vue';
import type { Position, Size, EditorState, MouseEventPosition } from '../types';
import { ZOOM_CONFIG, CANVAS_CONFIG } from '../constants';
import { canvasToGrid, gridToCanvas, snapToGrid, snapToSeatGrid } from '../utils';

export function useCanvas(
  canvasRef: ShallowRef<HTMLElement | null>,
  editorState: Ref<EditorState>,
) {

  /**
   * 屏幕坐标转画布坐标
   */
  const screenToCanvas = (screenPos: Position): Position => {
    if (!canvasRef.value) { return { x: 0, y: 0 }; }

    const rect = canvasRef.value.getBoundingClientRect();
    const x = (screenPos.x - rect.left) / editorState.value.scale;
    const y = (screenPos.y - rect.top) / editorState.value.scale;

    return { x, y };
  };

  /**
   * 画布坐标转网格坐标
   */
  const canvasToGridCoord = (canvasPos: Position): Position => {
    return canvasToGrid(canvasPos, CANVAS_CONFIG.GRID_SIZE);
  };

  /**
   * 网格坐标转画布坐标
   */
  const gridToCanvasCoord = (gridPos: Position): Position => {
    return gridToCanvas(gridPos, CANVAS_CONFIG.GRID_SIZE);
  };

  /**
   * 获取鼠标事件的多种坐标
   */
  const getMouseEventPosition = (event: MouseEvent): MouseEventPosition => {
    const screen = { x: event.clientX, y: event.clientY };
    const canvas = screenToCanvas(screen);
    const grid = canvasToGridCoord(canvas);

    return { screen, canvas, grid };
  };

  /**
   * 坐标吸附到网格
   */
  const snapPositionToGrid = (position: Position): Position => {
    return editorState.value.showGrid
      ? snapToGrid(position, CANVAS_CONFIG.GRID_SIZE)
      : position;
  };

  /**
   * 坐标吸附到座位网格
   */
  const snapPositionToSeatGrid = (position: Position): Position => {
    const defaultSpacing = { width: 40, height: 40 };
    return snapToSeatGrid(position, defaultSpacing);
  };


  /**
   * 放大
   */
  const zoomIn = () => {
    editorState.value.scale = Math.min(
      editorState.value.scale * ZOOM_CONFIG.STEP,
      ZOOM_CONFIG.MAX_SCALE,
    );
  };

  /**
   * 缩小
   */
  const zoomOut = () => {
    editorState.value.scale = Math.max(
      editorState.value.scale / ZOOM_CONFIG.STEP,
      ZOOM_CONFIG.MIN_SCALE,
    );
  };

  /**
   * 重置缩放
   */
  const zoomReset = () => {
    editorState.value.scale = ZOOM_CONFIG.DEFAULT;
  };

  /**
   * 设置特定缩放比例
   */
  const setZoom = (scale: number) => {
    editorState.value.scale = Math.max(
      ZOOM_CONFIG.MIN_SCALE,
      Math.min(scale, ZOOM_CONFIG.MAX_SCALE),
    );
  };

  /**
   * 适合窗口大小
   */
  const fitToView = (contentSize?: Size) => {
    if (!canvasRef.value || !contentSize) { return; }

    const container = canvasRef.value.parentElement;
    if (!container) { return; }

    const containerWidth = container.clientWidth;
    // const containerHeight = container.clientHeight;
    const availableWidth = containerWidth - CANVAS_CONFIG.EDITOR_PADDING.left - CANVAS_CONFIG.EDITOR_PADDING.right;
    // const availableHeight = containerHeight - CANVAS_CONFIG.EDITOR_PADDING.top - CANVAS_CONFIG.EDITOR_PADDING.bottom;

    const scaleX = availableWidth / contentSize.width;
    // const scaleY = availableHeight / contentSize.height;
    const scale = Math.min(scaleX, 3.0);
    const safeScale = Math.max(0.05, scale);

    setZoom(safeScale);
  };

  /**
   * 切换网格显示
   */
  const toggleGrid = () => {
    editorState.value.showGrid = !editorState.value.showGrid;
  };

  /**
   * 当前缩放比例的百分比显示
   */
  const scalePercentage = computed(() => {
    return Math.round(editorState.value.scale * 100);
  });

  /**
   * 画布变换样式
   */
  const canvasTransform = computed(() => ({
    transform: `scale(${editorState.value.scale}) translate(0px, 0px)`,
    transformOrigin: 'top center',
  }));

  /**
   * 是否可以放大
   */
  const canZoomIn = computed(() => {
    return editorState.value.scale < ZOOM_CONFIG.MAX_SCALE;
  });

  /**
   * 是否可以缩小
   */
  const canZoomOut = computed(() => {
    return editorState.value.scale > ZOOM_CONFIG.MIN_SCALE;
  });

  return {
    // 坐标转换
    screenToCanvas,
    canvasToGrid: canvasToGridCoord,
    gridToCanvas: gridToCanvasCoord,
    getMouseEventPosition,
    snapPositionToGrid,
    snapPositionToSeatGrid,

    // 缩放控制
    zoomIn,
    zoomOut,
    zoomReset,
    setZoom,

    // 视图控制
    fitToView,

    // 网格控制
    toggleGrid,

    // 计算属性
    scalePercentage,
    canvasTransform,
    canZoomIn,
    canZoomOut,
  };
}
