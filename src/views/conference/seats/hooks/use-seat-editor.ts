/**
 * 座位编辑相关的组合式函数
 */

import { ref, computed, type Ref } from 'vue';
import type {
  VenueLayout,
  SeatArea,
  Seat,
  EditorState,
  Position,
} from '../types';
import { getSeatAbsolutePosition } from '../utils';

export function useSeatEditor(
  layout: Ref<VenueLayout>,
  editorState: Ref<EditorState>,
) {
  // 座位编辑对话框状态
  const seatEditDialog = ref(false);
  const editingSeat = ref<Seat | null>(null);
  const editingArea = ref<SeatArea | null>(null);


  /**
   * 当前选中的座位
   */
  const selectedSeat = computed(() => {
    if (!editorState.value.selectedSeatId || !editorState.value.selectedAreaId) {
      return null;
    }

    const area = layout.value.areas.find(a => a.id === editorState.value.selectedAreaId);
    if (!area) { return null; }

    return area.seats.find(s => s.id === editorState.value.selectedSeatId) || null;
  });

  /**
   * 当前选中座位所在的区域
   */
  const selectedSeatArea = computed(() => {
    if (!editorState.value.selectedAreaId) { return null; }
    return layout.value.areas.find(a => a.id === editorState.value.selectedAreaId) || null;
  });

  /**
   * 当前选中座位的绝对位置
   */
  const selectedSeatPosition = computed(() => {
    if (!selectedSeat.value || !selectedSeatArea.value) { return null; }
    return getSeatAbsolutePosition(selectedSeat.value, selectedSeatArea.value);
  });


  /**
   * 根据ID查找座位
   */
  const findSeatById = (areaId: string, seatId: string): { area: SeatArea; seat: Seat } | null => {
    const area = layout.value.areas.find(a => a.id === areaId);
    if (!area) { return null; }

    const seat = area.seats.find(s => s.id === seatId);
    if (!seat) { return null; }

    return { area, seat };
  };

  /**
   * 根据座位号查找座位
   */
  const findSeatByNumber = (seatNumber: string): { area: SeatArea; seat: Seat } | null => {
    for (const area of layout.value.areas) {
      const seat = area.seats.find(s => s.seatNumber === seatNumber);
      if (seat) {
        return { area, seat };
      }
    }
    return null;
  };

  /**
   * 选中座位
   */
  const selectSeat = (areaId: string, seatId: string) => {
    editorState.value.selectedAreaId = areaId;
    editorState.value.selectedSeatId = seatId;
  };

  /**
   * 取消选中座位
   */
  const clearSeatSelection = () => {
    editorState.value.selectedSeatId = undefined;
  };

  /**
   * 开始编辑座位
   */
  const startEditSeat = (areaId: string, seatId: string) => {
    const result = findSeatById(areaId, seatId);
    if (!result) { return; }

    editingArea.value = result.area;
    editingSeat.value = { ...result.seat };

    seatEditDialog.value = true;
  };

  /**
   * 开始编辑当前选中的座位
   */
  const startEditSelectedSeat = () => {
    if (editorState.value.selectedAreaId && editorState.value.selectedSeatId) {
      startEditSeat(editorState.value.selectedAreaId, editorState.value.selectedSeatId);
    }
  };

  /**
   * 保存座位信息
   */
  const saveSeatInfo = (seatInfo: { name?: string; phone?: string }) => {
    if (!editingSeat.value || !editingArea.value) { return false; }

    const areaIndex = layout.value.areas.findIndex(a => a.id === editingArea.value!.id);
    if (areaIndex === -1) { return false; }

    const seatIndex = layout.value.areas[areaIndex].seats.findIndex(
      s => s.id === editingSeat.value!.id,
    );
    if (seatIndex === -1) { return false; }

    layout.value.areas[areaIndex].seats[seatIndex] = {
      ...editingSeat.value,
      name: seatInfo.name?.trim() || undefined,
      phone: seatInfo.phone?.trim() || undefined,
    };

    closeSeatEditDialog();

    return true;
  };

  /**
   * 关闭座位编辑对话框
   */
  const closeSeatEditDialog = () => {
    seatEditDialog.value = false;
    editingSeat.value = null;
    editingArea.value = null;
  };

  /**
   * 清空座位信息
   */
  const clearSeatInfo = (areaId: string, seatId: string): boolean => {
    const result = findSeatById(areaId, seatId);
    if (!result) { return false; }

    const areaIndex = layout.value.areas.findIndex(a => a.id === areaId);
    const seatIndex = layout.value.areas[areaIndex].seats.findIndex(s => s.id === seatId);

    layout.value.areas[areaIndex].seats[seatIndex] = {
      ...result.seat,
      name: undefined,
      phone: undefined,
    };

    return true;
  };

  /**
   * 清空选中座位的信息
   */
  const clearSelectedSeatInfo = (): boolean => {
    if (!editorState.value.selectedAreaId || !editorState.value.selectedSeatId) {
      return false;
    }
    return clearSeatInfo(editorState.value.selectedAreaId, editorState.value.selectedSeatId);
  };

  /**
   * 批量清空区域内所有座位信息
   */
  const clearAreaSeatsInfo = (areaId: string): boolean => {
    const areaIndex = layout.value.areas.findIndex(a => a.id === areaId);
    if (areaIndex === -1) { return false; }

    layout.value.areas[areaIndex].seats = layout.value.areas[areaIndex].seats.map(seat => ({
      ...seat,
      name: undefined,
      phone: undefined,
    }));

    return true;
  };

  /**
   * 批量更新区域内座位信息
   */
  const batchUpdateSeats = (
    areaId: string,
    updates: Array<{ seatId: string; name?: string; phone?: string }>,
  ): boolean => {
    const areaIndex = layout.value.areas.findIndex(a => a.id === areaId);
    if (areaIndex === -1) { return false; }

    const updatesMap = new Map(updates.map(update => [update.seatId, update]));

    layout.value.areas[areaIndex].seats = layout.value.areas[areaIndex].seats.map(seat => {
      const update = updatesMap.get(seat.id);
      if (update) {
        return {
          ...seat,
          name: update.name?.trim() || undefined,
          phone: update.phone?.trim() || undefined,
        };
      }
      return seat;
    });

    return true;
  };

  /**
   * 搜索座位
   */
  const searchSeats = (query: string) => {
    const results: Array<{ area: SeatArea; seat: Seat; position: Position }> = [];
    const searchQuery = query.toLowerCase().trim();

    if (!searchQuery) { return results; }

    for (const area of layout.value.areas) {
      for (const seat of area.seats) {
        let matched = false;

        if (seat.seatNumber.toLowerCase().includes(searchQuery)) {
          matched = true;
        }
        else if (seat.name && seat.name.toLowerCase().includes(searchQuery)) {
          matched = true;
        }
        else if (seat.phone && seat.phone.includes(searchQuery)) {
          matched = true;
        }

        if (matched) {
          results.push({
            area,
            seat,
            position: getSeatAbsolutePosition(seat, area),
          });
        }
      }
    }

    return results;
  };

  /**
   * 获取区域座位统计
   */
  const getAreaSeatStats = (areaId: string) => {
    const area = layout.value.areas.find(a => a.id === areaId);
    if (!area) { return null; }

    const totalSeats = area.seats.length;
    const occupiedSeats = area.seats.filter(seat => seat.name).length;
    const availableSeats = totalSeats - occupiedSeats;

    return {
      totalSeats,
      occupiedSeats,
      availableSeats,
      occupancyRate: totalSeats > 0 ? (occupiedSeats / totalSeats) * 100 : 0,
    };
  };

  /**
   * 验证手机号格式
   */
  const validatePhone = (phone: string): boolean => {
    if (!phone) { return true; }
    return /^1[3-9]\d{9}$/.test(phone);
  };

  /**
   * 验证座位信息
   */
  const validateSeatInfo = (seatInfo: { name?: string; phone?: string }): string[] => {
    const errors: string[] = [];

    if (seatInfo.name && seatInfo.name.length > 50) {
      errors.push('姓名不能超过50个字符');
    }

    if (seatInfo.phone && !validatePhone(seatInfo.phone)) {
      errors.push('手机号格式不正确');
    }

    return errors;
  };

  return {
    // 状态
    seatEditDialog,
    editingSeat,
    editingArea,
    selectedSeat,
    selectedSeatArea,
    selectedSeatPosition,

    // 查找
    findSeatById,
    findSeatByNumber,

    // 选择
    selectSeat,
    clearSeatSelection,

    // 编辑
    startEditSeat,
    startEditSelectedSeat,
    saveSeatInfo,
    closeSeatEditDialog,

    // 批量操作
    clearSeatInfo,
    clearSelectedSeatInfo,
    clearAreaSeatsInfo,
    batchUpdateSeats,

    // 搜索和统计
    searchSeats,
    getAreaSeatStats,

    // 验证
    validatePhone,
    validateSeatInfo,
  };
}
