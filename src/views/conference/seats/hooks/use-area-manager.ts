/**
 * 区域管理相关的组合式函数
 */

import { ref, computed, type Ref } from 'vue';
import type {
  SeatArea,
  VenueLayout,
  Position,
  GridSize,
  EditorState,
  AreaConfigDialog,
  ResizeDirection,
  ResizeState,
  ResizeConfirmDialog,
} from '../types';
import {
  createSeatArea,
  findAreaAtPosition,
  findSeatAtPosition,
  checkAreaOverlap,
  generateDefaultAreaName,
  calculateAreaPosition,
  calculateGridSize,
  calculateNewGridSize,
  checkAffectedSeats,
  validateResize,
  resizeArea,
} from '../utils';

export function useAreaManager(
  layout: Ref<VenueLayout>,
  editorState: Ref<EditorState>,
) {
  // 区域配置对话框状态
  const areaConfigDialog = ref<AreaConfigDialog>({
    visible: false,
    position: { x: 0, y: 0 },
    gridSize: { rows: 1, cols: 1 },
    defaultName: '',
    selectedName: '',
  });

  // 缩放状态
  const resizeState = ref<ResizeState>({
    isResizing: false,
    direction: null,
    targetAreaId: '',
    startPosition: { x: 0, y: 0 },
    currentPosition: { x: 0, y: 0 },
    originalGridSize: { rows: 0, cols: 0 },
  });

  // 缩放确认对话框状态
  const resizeConfirmDialog = ref<ResizeConfirmDialog>({
    visible: false,
    areaId: '',
    areaName: '',
    direction: 'right',
    newGridSize: { rows: 0, cols: 0 },
    affectedSeats: 0,
  });

  /**
   * 当前选中的区域
   */
  const selectedArea = computed(() => {
    if (!editorState.value.selectedAreaId) {
      return null;
    }
    return layout.value.areas.find(area => area.id === editorState.value.selectedAreaId) || null;
  });

  /**
   * 总区域数
   */
  const totalAreas = computed(() => layout.value.areas.length);

  /**
   * 总座位数
   */
  const totalSeats = computed(() => {
    return layout.value.areas.reduce((sum, area) => sum + area.seats.length, 0);
  });

  /**
   * 已预约座位数
   */
  const occupiedSeats = computed(() => {
    return layout.value.areas.reduce(
      (sum, area) => sum + area.seats.filter(seat => seat.name).length,
      0,
    );
  });

  /**
   * 根据位置查找区域
   */
  const findAreaAt = (position: Position): SeatArea | null => {
    return findAreaAtPosition(position, layout.value.areas);
  };

  /**
   * 根据位置查找座位
   */
  const findSeatAt = (position: Position) => {
    return findSeatAtPosition(position, layout.value.areas);
  };

  /**
   * 根据ID查找区域
   */
  const findAreaById = (areaId: string): SeatArea | null => {
    return layout.value.areas.find(area => area.id === areaId) || null;
  };

  /**
   * 验证区域放置是否有效
   */
  const validateAreaPlacement = (
    position: Position,
    gridSize: GridSize,
    excludeAreaId?: string,
  ): { valid: boolean; reason?: string } => {
    const defaultSpacing = { width: 40, height: 40 };
    const areaWidth = gridSize.cols * defaultSpacing.width;
    const areaHeight = gridSize.rows * defaultSpacing.height;

    const canvasWidth = layout.value.canvasGridDimensions.cols * 40;
    const canvasHeight = layout.value.canvasGridDimensions.rows * 40;

    if (position.x < 0 || position.y < 0) {
      return { valid: false, reason: '区域不能超出画布边界' };
    }

    if (position.x + areaWidth > canvasWidth ||
        position.y + areaHeight > canvasHeight) {
      return { valid: false, reason: '区域超出画布边界' };
    }

    const existingAreas = excludeAreaId
      ? layout.value.areas.filter(area => area.id !== excludeAreaId)
      : layout.value.areas;

    const tempArea = {
      position,
      gridSize,
      seatSpacing: defaultSpacing,
    };

    if (checkAreaOverlap(tempArea, existingAreas)) {
      return { valid: false, reason: '区域与现有区域重叠' };
    }

    return { valid: true };
  };

  /**
   * 开始创建区域
   */
  const startCreateArea = (startPos: Position, endPos: Position) => {
    const defaultSpacing = { width: 40, height: 40 };
    const position = calculateAreaPosition(startPos, endPos, defaultSpacing);
    const gridSize = calculateGridSize(startPos, endPos, defaultSpacing);

    const validation = validateAreaPlacement(position, gridSize);
    if (!validation.valid) {
      throw new Error(validation.reason);
    }

    const defaultName = generateDefaultAreaName(layout.value.areas);
    const newArea = createSeatArea(
      defaultName,
      position,
      gridSize,
    );

    layout.value.areas.push(newArea);
    editorState.value.selectedAreaId = newArea.id;
    return newArea;
  };

  /**
   * 确认创建区域
   */
  const confirmCreateArea = (config: {
    name: string;
    position: Position;
    gridSize: GridSize;
  }) => {
    const validation = validateAreaPlacement(config.position, config.gridSize);
    if (!validation.valid) {
      throw new Error(validation.reason);
    }
    const newArea = createSeatArea(
      config.name,
      config.position,
      config.gridSize,
    );

    layout.value.areas.push(newArea);
    editorState.value.selectedAreaId = newArea.id;
    areaConfigDialog.value.visible = false;

    return newArea;
  };

  /**
   * 取消创建区域
   */
  const cancelCreateArea = () => {
    areaConfigDialog.value.visible = false;
  };

  /**
   * 选中区域
   */
  const selectArea = (areaId: string) => {
    editorState.value.selectedAreaId = areaId;
    editorState.value.selectedSeatId = undefined;
  };

  /**
   * 取消选中
   */
  const clearSelection = () => {
    editorState.value.selectedAreaId = undefined;
    editorState.value.selectedSeatId = undefined;
  };

  /**
   * 更新区域
   */
  const updateArea = (updatedArea: SeatArea) => {
    const index = layout.value.areas.findIndex(area => area.id === updatedArea.id);
    if (index !== -1) {
      layout.value.areas[index] = {
        ...updatedArea,
        updatedAt: new Date().toISOString(),
      };
    }
  };

  /**
   * 移动区域
   */
  const moveArea = (areaId: string, newPosition: Position): boolean => {
    const area = findAreaById(areaId);
    if (!area) {
      return false;
    }

    const validation = validateAreaPlacement(newPosition, area.gridSize, areaId);
    if (!validation.valid) {
      return false;
    }
    updateArea({
      ...area,
      position: newPosition,
    });

    return true;
  };

  /**
   * 删除区域
   */
  const deleteArea = (areaId: string) => {
    const index = layout.value.areas.findIndex(area => area.id === areaId);
    if (index !== -1) {
      layout.value.areas.splice(index, 1);

      if (editorState.value.selectedAreaId === areaId) {
        clearSelection();
      }
    }
  };

  /**
   * 删除选中的区域
   */
  const deleteSelectedArea = () => {
    if (editorState.value.selectedAreaId) {
      deleteArea(editorState.value.selectedAreaId);
    }
  };

  /**
   * 清空所有区域
   */
  const clearAllAreas = () => {
    layout.value.areas = [];
    clearSelection();
  };

  /**
   * 编辑区域
   */
  const editArea = (areaId: string, newName: string): boolean => {
    const area = findAreaById(areaId);
    if (!area) {
      return false;
    }

    updateArea({
      ...area,
      name: newName,
    });

    return true;
  };

  // ============ 缩放相关函数 ============

  /**
   * 开始缩放操作
   */
  const startResize = (areaId: string, direction: ResizeDirection, startPos: Position) => {
    const area = findAreaById(areaId);
    if (!area) {
      return false;
    }

    resizeState.value = {
      isResizing: true,
      direction,
      targetAreaId: areaId,
      startPosition: startPos,
      currentPosition: startPos,
      originalGridSize: { ...area.gridSize },
    };

    return true;
  };

  /**
   * 更新缩放状态
   */
  const updateResize = (currentPos: Position) => {
    if (!resizeState.value.isResizing) {
      return;
    }

    resizeState.value.currentPosition = currentPos;
  };

  /**
   * 计算当前缩放的新网格尺寸
   */
  const currentResizeGridSize = computed(() => {
    if (!resizeState.value.isResizing) {
      return resizeState.value.originalGridSize;
    }

    const area = findAreaById(resizeState.value.targetAreaId);
    if (!area) {
      return resizeState.value.originalGridSize;
    }

    const deltaPosition = {
      x: resizeState.value.currentPosition.x - resizeState.value.startPosition.x,
      y: resizeState.value.currentPosition.y - resizeState.value.startPosition.y,
    };

    return calculateNewGridSize(
      resizeState.value.originalGridSize,
      resizeState.value.direction!,
      deltaPosition,
      area.seatSpacing,
    );
  });

  /**
   * 完成缩放操作
   */
  const finishResize = () => {
    if (!resizeState.value.isResizing) {
      return;
    }

    const area = findAreaById(resizeState.value.targetAreaId);
    if (!area) {
      cancelResize();
      return;
    }

    const newGridSize = currentResizeGridSize.value;

    // 验证缩放操作
    const validation = validateResize(
      area,
      newGridSize,
      layout.value.canvasGridDimensions,
      layout.value.areas,
    );

    if (!validation.valid) {
      // 验证失败时要保持缩放状态，不能直接cancelResize
      // 让调用方处理错误后再调用cancelResize
      throw new Error(validation.reason);
    }

    // 检查是否会影响已填写信息的座位
    const affectedSeats = checkAffectedSeats(area, newGridSize);

    if (affectedSeats > 0) {
      // 显示确认对话框
      resizeConfirmDialog.value = {
        visible: true,
        areaId: area.id,
        areaName: area.name,
        direction: resizeState.value.direction!,
        newGridSize,
        affectedSeats,
      };
    } else {
      // 直接执行缩放
      executeResize(area.id, newGridSize);
    }
  };

  /**
   * 执行缩放操作
   */
  const executeResize = (areaId: string, newGridSize: GridSize) => {
    const area = findAreaById(areaId);
    if (!area) {
      return false;
    }

    const updatedArea = resizeArea(area, newGridSize);
    updateArea(updatedArea);
    cancelResize();
    return true;
  };

  /**
   * 确认缩放操作
   */
  const confirmResize = () => {
    const { areaId, newGridSize } = resizeConfirmDialog.value;
    executeResize(areaId, newGridSize);
    resizeConfirmDialog.value.visible = false;
  };

  /**
   * 取消缩放操作
   */
  const cancelResize = () => {
    resizeState.value = {
      isResizing: false,
      direction: null,
      targetAreaId: '',
      startPosition: { x: 0, y: 0 },
      currentPosition: { x: 0, y: 0 },
      originalGridSize: { rows: 0, cols: 0 },
    };
  };

  /**
   * 取消缩放确认对话框
   */
  const cancelResizeConfirm = () => {
    resizeConfirmDialog.value.visible = false;
    cancelResize();
  };

  return {
    // 状态
    areaConfigDialog,
    resizeState,
    resizeConfirmDialog,
    selectedArea,
    totalAreas,
    totalSeats,
    occupiedSeats,
    currentResizeGridSize,

    // 查找
    findAreaAt,
    findSeatAt,
    findAreaById,

    // 验证
    validateAreaPlacement,

    // 创建
    startCreateArea,
    confirmCreateArea,
    cancelCreateArea,

    // 操作
    selectArea,
    clearSelection,
    updateArea,
    moveArea,
    deleteArea,
    deleteSelectedArea,
    clearAllAreas,

    // 编辑
    editArea,

    // 缩放
    startResize,
    updateResize,
    finishResize,
    executeResize,
    confirmResize,
    cancelResize,
    cancelResizeConfirm,
  };
}
