/**
 * 座位编排功能的工具函数
 */

import type {
  Position,
  Size,
  GridSize,
  SeatArea,
  Seat,
  VenueLayout,
  SeatSearchResult,
  ExportLayoutData,
  SimpleSeatData,
  SimpleAreaInfo,
  ResizeDirection,
} from './types';

/**
 * 生成唯一ID
 */
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 11);
}


/**
 * 将画布坐标转换为网格坐标
 */
export function canvasToGrid(canvasPosition: Position, gridSize: number): Position {
  return {
    x: Math.floor(canvasPosition.x / gridSize),
    y: Math.floor(canvasPosition.y / gridSize),
  };
}

/**
 * 将网格坐标转换为画布坐标
 */
export function gridToCanvas(gridPosition: Position, gridSize: number): Position {
  return {
    x: gridPosition.x * gridSize,
    y: gridPosition.y * gridSize,
  };
}

/**
 * 坐标吸附到网格
 */
export function snapToGrid(position: Position, gridSize: number): Position {
  return gridToCanvas(canvasToGrid(position, gridSize), gridSize);
}

/**
 * 坐标吸附到座位网格
 */
export function snapToSeatGrid(position: Position, seatSpacing: Size): Position {
  return {
    x: Math.floor(position.x / seatSpacing.width) * seatSpacing.width,
    y: Math.floor(position.y / seatSpacing.height) * seatSpacing.height,
  };
}


/**
 * 计算拖拽区域的网格尺寸
 */
export function calculateGridSize(startPos: Position, endPos: Position, seatSpacing: Size): GridSize {
  const startGridX = Math.floor(startPos.x / seatSpacing.width);
  const startGridY = Math.floor(startPos.y / seatSpacing.height);
  const endGridX = Math.floor(endPos.x / seatSpacing.width);
  const endGridY = Math.floor(endPos.y / seatSpacing.height);

  return {
    rows: Math.abs(endGridY - startGridY) + 1,
    cols: Math.abs(endGridX - startGridX) + 1,
  };
}

/**
 * 计算拖拽区域的起始位置
 */
export function calculateAreaPosition(startPos: Position, endPos: Position, seatSpacing: Size): Position {
  const startGridX = Math.floor(startPos.x / seatSpacing.width);
  const startGridY = Math.floor(startPos.y / seatSpacing.height);
  const endGridX = Math.floor(endPos.x / seatSpacing.width);
  const endGridY = Math.floor(endPos.y / seatSpacing.height);

  return {
    x: Math.min(startGridX, endGridX) * seatSpacing.width,
    y: Math.min(startGridY, endGridY) * seatSpacing.height,
  };
}

/**
 * 计算区域的边界框
 */
export function getAreaBounds(area: SeatArea): {
  left: number;
  top: number;
  right: number;
  bottom: number;
} {
  return {
    left: area.position.x,
    top: area.position.y,
    right: area.position.x + (area.gridSize.cols * area.seatSpacing.width),
    bottom: area.position.y + (area.gridSize.rows * area.seatSpacing.height),
  };
}

/**
 * 检查点是否在区域内
 */
export function isPointInArea(point: Position, area: SeatArea): boolean {
  const bounds = getAreaBounds(area);
  return point.x >= bounds.left &&
         point.x <= bounds.right &&
         point.y >= bounds.top &&
         point.y <= bounds.bottom;
}

/**
 * 检查区域是否与现有区域重叠
 */
export function checkAreaOverlap(
  newArea: { position: Position; gridSize: GridSize; seatSpacing: Size },
  existingAreas: SeatArea[],
): boolean {
  const newBounds = {
    left: newArea.position.x,
    top: newArea.position.y,
    right: newArea.position.x + (newArea.gridSize.cols * newArea.seatSpacing.width),
    bottom: newArea.position.y + (newArea.gridSize.rows * newArea.seatSpacing.height),
  };

  for (const area of existingAreas) {
    const areaBounds = getAreaBounds(area);

    // 检查是否有重叠
    if (!(newBounds.right <= areaBounds.left ||
          areaBounds.right <= newBounds.left ||
          newBounds.bottom <= areaBounds.top ||
          areaBounds.bottom <= newBounds.top)) {
      return true; // 有重叠
    }
  }

  return false; // 无重叠
}


/**
 * 生成默认区域名称
 */
export function generateDefaultAreaName(existingAreas: SeatArea[]): string {
  const nextNumber = existingAreas.length + 1;
  return `区域${nextNumber}`;
}


/**
 * 生成座位号
 */
export function generateSeatNumber(_area: SeatArea, row: number, col: number): string {
  const rowLetter = String.fromCharCode(65 + row); // A, B, C...
  const colNumber = (col + 1).toString().padStart(2, '0'); // 01, 02, 03...
  return `${rowLetter}${colNumber}`;
}



/**
 * 为区域生成座位
 */
export function generateSeatsForArea(area: SeatArea): Seat[] {
  const seats: Seat[] = [];
  const seatMargin = {
    x: (area.seatSpacing.width - 30) / 2,
    y: (area.seatSpacing.height - 30) / 2,
  };

  for (let row = 0; row < area.gridSize.rows; row++) {
    for (let col = 0; col < area.gridSize.cols; col++) {
      const seat: Seat = {
        id: generateId(),
        areaId: area.id,
        position: {
          x: col * area.seatSpacing.width + seatMargin.x,
          y: row * area.seatSpacing.height + seatMargin.y,
        },
        row,
        col,
        seatNumber: generateSeatNumber(area, row, col),
      };
      seats.push(seat);
    }
  }

  return seats;
}

/**
 * 创建座位区域
 */
export function createSeatArea(
  name: string,
  position: Position,
  gridSize: GridSize,
): SeatArea {
  const areaId = generateId();
  const defaultSpacing = { width: 40, height: 40 };

  const area: SeatArea = {
    id: areaId,
    name,
    position,
    gridSize,
    seatSpacing: defaultSpacing,
    seats: [],
    color: '#f0f0f0',
    borderColor: '#d0d0d0',
    createdAt: new Date().toISOString(),
  };

  area.seats = generateSeatsForArea(area);

  return area;
}

/**
 * 计算座位的绝对位置
 */
export function getSeatAbsolutePosition(seat: Seat, area: SeatArea): Position {
  return {
    x: area.position.x + seat.position.x,
    y: area.position.y + seat.position.y,
  };
}


/**
 * 查找位置上的区域
 */
export function findAreaAtPosition(position: Position, areas: SeatArea[]): SeatArea | null {
  // 从后往前查找（最后创建的在最上层）
  for (let i = areas.length - 1; i >= 0; i--) {
    const area = areas[i];
    if (isPointInArea(position, area)) {
      return area;
    }
  }
  return null;
}

/**
 * 查找位置上的座位
 */
export function findSeatAtPosition(position: Position, areas: SeatArea[]): { area: SeatArea; seat: Seat } | null {
  const area = findAreaAtPosition(position, areas);
  if (!area) { return null; }

  const relativePos = {
    x: position.x - area.position.x,
    y: position.y - area.position.y,
  };

  for (const seat of area.seats) {
    const seatBounds = {
      left: seat.position.x,
      top: seat.position.y,
      right: seat.position.x + 30,
      bottom: seat.position.y + 30,
    };

    if (relativePos.x >= seatBounds.left &&
        relativePos.x <= seatBounds.right &&
        relativePos.y >= seatBounds.top &&
        relativePos.y <= seatBounds.bottom) {
      return { area, seat };
    }
  }

  return null;
}


/**
 * 搜索座位
 */
export function searchSeats(
  layout: VenueLayout,
  query: string,
): SeatSearchResult[] {
  const results: SeatSearchResult[] = [];
  const searchQuery = query.toLowerCase().trim();

  if (!searchQuery) { return results; }

  for (const area of layout.areas) {
    for (const seat of area.seats) {
      let matched = false;

      if (seat.seatNumber.toLowerCase().includes(searchQuery)) {
        matched = true;
      }
      else if (seat.name && seat.name.toLowerCase().includes(searchQuery)) {
        matched = true;
      }
      else if (seat.phone && seat.phone.includes(searchQuery)) {
        matched = true;
      }
      else if (area.name.toLowerCase().includes(searchQuery)) {
        matched = true;
      }

      if (matched) {
        results.push({
          seat,
          area,
          absolutePosition: getSeatAbsolutePosition(seat, area),
        });
      }
    }
  }

  return results;
}


/**
 * 导出座位布局数据
 */
export function exportLayoutData(layout: VenueLayout): ExportLayoutData {
  if (layout.areas.length === 0) {
    return {
      seats: [],
      areas: [],
      totalRows: 0,
      totalCols: 0,
    };
  }

  let minRow = Infinity;
  let maxRow = -Infinity;
  let minCol = Infinity;
  let maxCol = -Infinity;
  const allSeats: Array<{
    seat: Seat;
    area: SeatArea;
    globalRow: number;
    globalCol: number;
  }> = [];

  for (const area of layout.areas) {
    const areaStartRow = Math.floor(area.position.y / area.seatSpacing.height);
    const areaStartCol = Math.floor(area.position.x / area.seatSpacing.width);

    for (const seat of area.seats) {
      const globalRow = areaStartRow + seat.row;
      const globalCol = areaStartCol + seat.col;

      minRow = Math.min(minRow, globalRow);
      maxRow = Math.max(maxRow, globalRow);
      minCol = Math.min(minCol, globalCol);
      maxCol = Math.max(maxCol, globalCol);

      allSeats.push({
        seat,
        area,
        globalRow,
        globalCol,
      });
    }
  }

  if (allSeats.length === 0) {
    return {
      seats: [],
      areas: [],
      totalRows: 0,
      totalCols: 0,
    };
  }

  const totalRows = maxRow - minRow + 1;
  const totalCols = maxCol - minCol + 1;
  const seats: (SimpleSeatData | null)[][] = Array(totalRows)
    .fill(null)
    .map(() => Array(totalCols).fill(null));

  for (const { seat, area, globalRow, globalCol } of allSeats) {
    const row = globalRow - minRow;
    const col = globalCol - minCol;

    seats[row][col] = {
      name: seat.name || '',
      phone: seat.phone || '',
      seatNumber: seat.seatNumber,
      areaName: area.name,
    };
  }

  const areas: SimpleAreaInfo[] = layout.areas.map(area => {
    const areaStartRow = Math.floor(area.position.y / area.seatSpacing.height);
    const areaStartCol = Math.floor(area.position.x / area.seatSpacing.width);

    return {
      name: area.name,
      startRow: areaStartRow - minRow,
      startCol: areaStartCol - minCol,
      rows: area.gridSize.rows,
      cols: area.gridSize.cols,
    };
  });

  return {
    seats,
    areas,
    totalRows,
    totalCols,
  };
}


/**
 * 验证布局数据
 */
export function validateLayout(layout: VenueLayout): string[] {
  const errors: string[] = [];

  for (let i = 0; i < layout.areas.length; i++) {
    for (let j = i + 1; j < layout.areas.length; j++) {
      const area1 = layout.areas[i];
      const area2 = layout.areas[j];

      if (checkAreaOverlap(area1, [area2])) {
        errors.push(`区域 "${area1.name}" 和 "${area2.name}" 重叠`);
      }
    }
  }

  return errors;
}


/**
 * 计算布局统计信息
 */
export function calculateLayoutStats(layout: VenueLayout) {
  const totalAreas = layout.areas.length;
  const totalSeats = layout.areas.reduce((sum, area) => sum + area.seats.length, 0);
  const occupiedSeats = layout.areas.reduce(
    (sum, area) => sum + area.seats.filter(seat => seat.name).length,
    0,
  );

  return {
    totalAreas,
    totalSeats,
    occupiedSeats,
    availableSeats: totalSeats - occupiedSeats,
  };
}


// ============ 缩放相关工具函数 ============

/**
 * 根据缩放方向和鼠标移动计算新的网格尺寸
 */
export function calculateNewGridSize(
  originalGridSize: GridSize,
  direction: ResizeDirection,
  deltaPosition: Position,
  seatSpacing: Size,
): GridSize {
  const deltaRows = Math.round(deltaPosition.y / seatSpacing.height);
  const deltaCols = Math.round(deltaPosition.x / seatSpacing.width);

  let newRows = originalGridSize.rows;
  let newCols = originalGridSize.cols;

  switch (direction) {
    case 'right':
      newCols = Math.max(1, originalGridSize.cols + deltaCols);
      break;
    case 'bottom':
      newRows = Math.max(1, originalGridSize.rows + deltaRows);
      break;
    case 'bottom-right':
      newRows = Math.max(1, originalGridSize.rows + deltaRows);
      newCols = Math.max(1, originalGridSize.cols + deltaCols);
      break;
    default:
      break;
  }

  return { rows: newRows, cols: newCols };
}

/**
 * 检查缩小操作会影响哪些已填写信息的座位
 */
export function checkAffectedSeats(
  area: SeatArea,
  newGridSize: GridSize,
): number {
  let affectedCount = 0;

  for (const seat of area.seats) {
    // 检查座位是否在新的网格范围外
    if (seat.row >= newGridSize.rows || seat.col >= newGridSize.cols) {
      // 检查是否有填写信息
      if (seat.name || seat.phone) {
        affectedCount++;
      }
    }
  }

  return affectedCount;
}

/**
 * 验证缩放操作是否有效
 */
export function validateResize(
  area: SeatArea,
  newGridSize: GridSize,
  canvasGridDimensions: GridSize,
  existingAreas: SeatArea[],
): { valid: boolean; reason?: string } {
  // 检查画布边界
  const areaWidth = newGridSize.cols * area.seatSpacing.width;
  const areaHeight = newGridSize.rows * area.seatSpacing.height;
  const canvasWidth = canvasGridDimensions.cols * 40;
  const canvasHeight = canvasGridDimensions.rows * 40;

  if (area.position.x + areaWidth > canvasWidth) {
    return { valid: false, reason: '缩放后区域超出画布右边界' };
  }

  if (area.position.y + areaHeight > canvasHeight) {
    return { valid: false, reason: '缩放后区域超出画布下边界' };
  }

  // 检查与其他区域重叠
  const tempArea = {
    position: area.position,
    gridSize: newGridSize,
    seatSpacing: area.seatSpacing,
  };

  const otherAreas = existingAreas.filter(a => a.id !== area.id);
  if (checkAreaOverlap(tempArea, otherAreas)) {
    return { valid: false, reason: '缩放后区域与其他区域重叠' };
  }

  return { valid: true };
}

/**
 * 保留现有座位数据并重新生成座位布局
 */
export function preserveSeatData(area: SeatArea, newGridSize: GridSize): Seat[] {
  // 创建现有座位数据的映射表，按行列索引存储
  const existingSeatData = new Map<string, { name?: string; phone?: string }>();
  for (const seat of area.seats) {
    if (seat.name || seat.phone) {
      const key = `${seat.row}-${seat.col}`;
      existingSeatData.set(key, {
        name: seat.name,
        phone: seat.phone,
      });
    }
  }

  // 重新生成座位布局
  const newSeats: Seat[] = [];
  const seatMargin = {
    x: (area.seatSpacing.width - 30) / 2,
    y: (area.seatSpacing.height - 30) / 2,
  };

  for (let row = 0; row < newGridSize.rows; row++) {
    for (let col = 0; col < newGridSize.cols; col++) {
      const seat: Seat = {
        id: generateId(),
        areaId: area.id,
        position: {
          x: col * area.seatSpacing.width + seatMargin.x,
          y: row * area.seatSpacing.height + seatMargin.y,
        },
        row,
        col,
        seatNumber: generateSeatNumber(area, row, col),
      };

      // 如果有现有数据，保留
      const key = `${row}-${col}`;
      const existingData = existingSeatData.get(key);
      if (existingData) {
        seat.name = existingData.name;
        seat.phone = existingData.phone;
      }

      newSeats.push(seat);
    }
  }

  return newSeats;
}

/**
 * 执行区域缩放操作
 */
export function resizeArea(
  area: SeatArea,
  newGridSize: GridSize,
): SeatArea {
  const updatedArea: SeatArea = {
    ...area,
    gridSize: newGridSize,
    seats: preserveSeatData(area, newGridSize),
    updatedAt: new Date().toISOString(),
  };

  return updatedArea;
}
