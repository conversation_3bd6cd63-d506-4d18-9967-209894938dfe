/**
 * 导出相关工具函数
 * 处理布局数据的导出和格式转换
 */

import type {
  VenueLayout,
  SeatArea,
  Seat,
  ExportLayoutData,
  SimpleSeatData,
  SimpleAreaInfo,
  SeatSearchResult,
} from '../types';
import { getSeatAbsolutePosition, isSeatOccupied } from './seat';

/**
 * 导出布局数据为前台使用的格式
 */
export function exportLayoutData(layout: VenueLayout): ExportLayoutData {
  const areas: SimpleAreaInfo[] = [];
  const seats: SimpleSeatData[] = [];

  for (const area of layout.areas) {
    // 导出区域信息
    areas.push({
      id: area.id,
      name: area.name,
      position: { ...area.position },
      gridSize: { ...area.gridSize },
      seatCount: area.seats.length,
      occupiedCount: area.seats.filter(isSeatOccupied).length,
    });

    // 导出座位信息
    for (const seat of area.seats) {
      if (isSeatOccupied(seat)) {
        const absolutePosition = getSeatAbsolutePosition(seat, area);
        
        seats.push({
          id: seat.id,
          areaId: seat.areaId,
          areaName: area.name,
          seatNumber: seat.seatNumber,
          name: seat.name || '',
          phone: seat.phone || '',
          position: absolutePosition,
          row: seat.row,
          col: seat.col,
        });
      }
    }
  }

  return {
    canvasSize: {
      width: layout.canvasGridDimensions.cols * layout.gridSize,
      height: layout.canvasGridDimensions.rows * layout.gridSize,
    },
    gridSize: layout.gridSize,
    areas,
    seats,
    totalAreas: areas.length,
    totalSeats: layout.areas.reduce((sum, area) => sum + area.seats.length, 0),
    occupiedSeats: seats.length,
    exportTime: new Date().toISOString(),
  };
}

/**
 * 搜索座位
 */
export function searchSeats(
  query: string,
  areas: SeatArea[]
): SeatSearchResult[] {
  const results: SeatSearchResult[] = [];
  const searchTerm = query.toLowerCase().trim();

  if (!searchTerm) {
    return results;
  }

  for (const area of areas) {
    for (const seat of area.seats) {
      let matches = false;

      // 按座位号搜索
      if (seat.seatNumber.toLowerCase().includes(searchTerm)) {
        matches = true;
      }

      // 按姓名搜索
      if (seat.name && seat.name.toLowerCase().includes(searchTerm)) {
        matches = true;
      }

      // 按手机号搜索
      if (seat.phone && seat.phone.includes(searchTerm)) {
        matches = true;
      }

      // 按区域名称搜索
      if (area.name.toLowerCase().includes(searchTerm)) {
        matches = true;
      }

      if (matches) {
        results.push({
          seat,
          area,
          absolutePosition: getSeatAbsolutePosition(seat, area),
        });
      }
    }
  }

  return results;
}

/**
 * 导出为CSV格式
 */
export function exportToCSV(layout: VenueLayout): string {
  const headers = ['区域名称', '座位号', '姓名', '手机号', '行号', '列号'];
  const rows: string[][] = [headers];

  for (const area of layout.areas) {
    for (const seat of area.seats) {
      if (isSeatOccupied(seat)) {
        rows.push([
          area.name,
          seat.seatNumber,
          seat.name || '',
          seat.phone || '',
          (seat.row + 1).toString(),
          (seat.col + 1).toString(),
        ]);
      }
    }
  }

  return rows.map(row => 
    row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')
  ).join('\n');
}

/**
 * 导出为Excel格式的数据
 */
export function exportToExcelData(layout: VenueLayout): Array<Record<string, any>> {
  const data: Array<Record<string, any>> = [];

  for (const area of layout.areas) {
    for (const seat of area.seats) {
      if (isSeatOccupied(seat)) {
        data.push({
          '区域名称': area.name,
          '座位号': seat.seatNumber,
          '姓名': seat.name || '',
          '手机号': seat.phone || '',
          '行号': seat.row + 1,
          '列号': seat.col + 1,
          '创建时间': area.createdAt || '',
        });
      }
    }
  }

  return data;
}

/**
 * 生成统计报告
 */
export function generateStatisticsReport(layout: VenueLayout): {
  totalAreas: number;
  totalSeats: number;
  occupiedSeats: number;
  occupancyRate: number;
  areaStatistics: Array<{
    areaName: string;
    totalSeats: number;
    occupiedSeats: number;
    occupancyRate: number;
  }>;
} {
  const totalSeats = layout.areas.reduce((sum, area) => sum + area.seats.length, 0);
  const occupiedSeats = layout.areas.reduce(
    (sum, area) => sum + area.seats.filter(isSeatOccupied).length,
    0
  );

  const areaStatistics = layout.areas.map(area => {
    const areaOccupiedSeats = area.seats.filter(isSeatOccupied).length;
    return {
      areaName: area.name,
      totalSeats: area.seats.length,
      occupiedSeats: areaOccupiedSeats,
      occupancyRate: area.seats.length > 0 ? (areaOccupiedSeats / area.seats.length) * 100 : 0,
    };
  });

  return {
    totalAreas: layout.areas.length,
    totalSeats,
    occupiedSeats,
    occupancyRate: totalSeats > 0 ? (occupiedSeats / totalSeats) * 100 : 0,
    areaStatistics,
  };
}

/**
 * 验证导入数据格式
 */
export function validateImportData(data: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!data || typeof data !== 'object') {
    errors.push('数据格式不正确');
    return { valid: false, errors };
  }

  if (!data.areas || !Array.isArray(data.areas)) {
    errors.push('缺少区域数据');
  }

  if (!data.canvasGridDimensions || 
      typeof data.canvasGridDimensions.cols !== 'number' ||
      typeof data.canvasGridDimensions.rows !== 'number') {
    errors.push('画布尺寸数据不正确');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}
