/**
 * 坐标转换相关工具函数
 * 处理画布坐标、网格坐标、屏幕坐标之间的转换
 */

import type { Position, MutablePosition, Size } from '../types';

/**
 * 生成唯一ID
 */
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 11);
}

/**
 * 将画布坐标转换为网格坐标
 */
export function canvasToGrid(canvasPosition: Position, gridSize: number): Position {
  return {
    x: Math.floor(canvasPosition.x / gridSize),
    y: Math.floor(canvasPosition.y / gridSize),
  };
}

/**
 * 将网格坐标转换为画布坐标
 */
export function gridToCanvas(gridPosition: Position, gridSize: number): Position {
  return {
    x: gridPosition.x * gridSize,
    y: gridPosition.y * gridSize,
  };
}

/**
 * 坐标吸附到网格
 */
export function snapToGrid(position: Position, gridSize: number): Position {
  return gridToCanvas(canvasToGrid(position, gridSize), gridSize);
}

/**
 * 坐标吸附到座位网格
 */
export function snapToSeatGrid(position: Position, seatSpacing: Size): Position {
  return {
    x: Math.floor(position.x / seatSpacing.width) * seatSpacing.width,
    y: Math.floor(position.y / seatSpacing.height) * seatSpacing.height,
  };
}

/**
 * 创建可变位置对象
 */
export function createMutablePosition(x: number, y: number): MutablePosition {
  return { x, y };
}

/**
 * 复制位置对象
 */
export function clonePosition(position: Position): MutablePosition {
  return { x: position.x, y: position.y };
}

/**
 * 计算两点之间的距离
 */
export function calculateDistance(pos1: Position, pos2: Position): number {
  const dx = pos2.x - pos1.x;
  const dy = pos2.y - pos1.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * 检查点是否在矩形区域内
 */
export function isPointInRect(
  point: Position,
  rect: { x: number; y: number; width: number; height: number }
): boolean {
  return (
    point.x >= rect.x &&
    point.x <= rect.x + rect.width &&
    point.y >= rect.y &&
    point.y <= rect.y + rect.height
  );
}

/**
 * 限制坐标在指定范围内
 */
export function clampPosition(
  position: Position,
  bounds: { minX: number; minY: number; maxX: number; maxY: number }
): Position {
  return {
    x: Math.max(bounds.minX, Math.min(position.x, bounds.maxX)),
    y: Math.max(bounds.minY, Math.min(position.y, bounds.maxY)),
  };
}
