/**
 * 座位操作相关工具函数
 * 处理座位的创建、查找、编辑等操作
 */

import type {
  Position,
  MutablePosition,
  SeatArea,
  Seat,
  SeatFindResult,
  SeatEditInfo,
} from '../types';
import { generateId, isPointInRect } from './coordinate';

/**
 * 生成座位号
 * @param area 区域信息
 * @param row 行号（从0开始）
 * @param col 列号（从0开始）
 */
export function generateSeatNumber(area: SeatArea, row: number, col: number): string {
  const rowLetter = String.fromCharCode(65 + row); // A, B, C...
  const colNumber = (col + 1).toString().padStart(2, '0'); // 01, 02, 03...
  return `${rowLetter}${colNumber}`;
}

/**
 * 为区域生成所有座位
 */
export function generateSeatsForArea(area: SeatArea): Seat[] {
  const seats: Seat[] = [];
  const seatMargin = {
    x: (area.seatSpacing.width - 30) / 2,
    y: (area.seatSpacing.height - 30) / 2,
  };

  for (let row = 0; row < area.gridSize.rows; row++) {
    for (let col = 0; col < area.gridSize.cols; col++) {
      const seatId = generateId();
      const position: MutablePosition = {
        x: col * area.seatSpacing.width + seatMargin.x,
        y: row * area.seatSpacing.height + seatMargin.y,
      };

      const seat: Seat = {
        id: seatId,
        areaId: area.id,
        position,
        row,
        col,
        seatNumber: generateSeatNumber(area, row, col),
      };

      seats.push(seat);
    }
  }

  return seats;
}

/**
 * 计算座位的绝对位置
 */
export function getSeatAbsolutePosition(seat: Seat, area: SeatArea): Position {
  return {
    x: area.position.x + seat.position.x,
    y: area.position.y + seat.position.y,
  };
}

/**
 * 根据位置查找座位
 */
export function findSeatAtPosition(position: Position, areas: SeatArea[]): SeatFindResult | null {
  for (const area of areas) {
    // 首先检查位置是否在区域内
    const areaWidth = area.gridSize.cols * area.seatSpacing.width;
    const areaHeight = area.gridSize.rows * area.seatSpacing.height;
    
    if (!isPointInRect(position, {
      x: area.position.x,
      y: area.position.y,
      width: areaWidth,
      height: areaHeight,
    })) {
      continue;
    }

    // 在区域内查找具体座位
    for (const seat of area.seats) {
      const seatAbsolutePos = getSeatAbsolutePosition(seat, area);
      
      if (isPointInRect(position, {
        x: seatAbsolutePos.x,
        y: seatAbsolutePos.y,
        width: 30, // 座位宽度
        height: 30, // 座位高度
      })) {
        return { seat, area };
      }
    }
  }

  return null;
}

/**
 * 根据ID查找座位
 */
export function findSeatById(areaId: string, seatId: string, areas: SeatArea[]): SeatFindResult | null {
  const area = areas.find(a => a.id === areaId);
  if (!area) return null;

  const seat = area.seats.find(s => s.id === seatId);
  if (!seat) return null;

  return { seat, area };
}

/**
 * 根据座位号查找座位
 */
export function findSeatByNumber(seatNumber: string, areas: SeatArea[]): SeatFindResult | null {
  for (const area of areas) {
    const seat = area.seats.find(s => s.seatNumber === seatNumber);
    if (seat) {
      return { seat, area };
    }
  }
  return null;
}

/**
 * 验证座位信息
 */
export function validateSeatInfo(seatInfo: SeatEditInfo): string[] {
  const errors: string[] = [];

  if (seatInfo.name && seatInfo.name.length > 50) {
    errors.push('姓名不能超过50个字符');
  }

  if (seatInfo.phone && !validatePhone(seatInfo.phone)) {
    errors.push('手机号格式不正确');
  }

  return errors;
}

/**
 * 验证手机号格式
 */
export function validatePhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

/**
 * 更新座位信息
 */
export function updateSeatInfo(
  seat: Seat,
  seatInfo: SeatEditInfo
): Seat {
  return {
    ...seat,
    name: seatInfo.name?.trim() || undefined,
    phone: seatInfo.phone?.trim() || undefined,
  };
}

/**
 * 清空座位信息
 */
export function clearSeatInfo(seat: Seat): Seat {
  return {
    ...seat,
    name: undefined,
    phone: undefined,
  };
}

/**
 * 检查座位是否已被预约
 */
export function isSeatOccupied(seat: Seat): boolean {
  return !!(seat.name || seat.phone);
}

/**
 * 统计区域内已预约座位数
 */
export function countOccupiedSeats(area: SeatArea): number {
  return area.seats.filter(isSeatOccupied).length;
}

/**
 * 统计所有区域的已预约座位数
 */
export function countTotalOccupiedSeats(areas: SeatArea[]): number {
  return areas.reduce((sum, area) => sum + countOccupiedSeats(area), 0);
}
