/**
 * 验证相关工具函数
 * 处理布局验证、区域验证、座位验证等
 */

import type {
  VenueLayout,
  SeatArea,
  Position,
  GridSize,
  ResizeDirection,
  AreaValidationResult,
} from '../types';
import { getAreaBounds, checkAreaOverlap, DEFAULT_SEAT_SPACING } from './area';
import { isSeatOccupied } from './seat';

/**
 * 验证布局数据
 */
export function validateLayout(layout: VenueLayout): string[] {
  const errors: string[] = [];

  // 验证画布尺寸
  if (layout.canvasGridDimensions.cols <= 0 || layout.canvasGridDimensions.rows <= 0) {
    errors.push('画布尺寸必须大于0');
  }

  // 验证网格大小
  if (layout.gridSize <= 0) {
    errors.push('网格大小必须大于0');
  }

  // 验证区域
  for (const area of layout.areas) {
    const areaErrors = validateArea(area, layout);
    errors.push(...areaErrors);
  }

  // 检查区域重叠
  for (let i = 0; i < layout.areas.length; i++) {
    for (let j = i + 1; j < layout.areas.length; j++) {
      const area1 = layout.areas[i];
      const area2 = layout.areas[j];
      
      if (checkAreaOverlap(area1, [area2])) {
        errors.push(`区域 "${area1.name}" 与区域 "${area2.name}" 重叠`);
      }
    }
  }

  return errors;
}

/**
 * 验证单个区域
 */
export function validateArea(area: SeatArea, layout: VenueLayout): string[] {
  const errors: string[] = [];

  // 验证区域名称
  if (!area.name || area.name.trim().length === 0) {
    errors.push(`区域 ${area.id} 名称不能为空`);
  }

  // 验证区域位置
  if (area.position.x < 0 || area.position.y < 0) {
    errors.push(`区域 "${area.name}" 位置不能为负数`);
  }

  // 验证区域尺寸
  if (area.gridSize.rows <= 0 || area.gridSize.cols <= 0) {
    errors.push(`区域 "${area.name}" 尺寸必须大于0`);
  }

  // 验证区域是否超出画布边界
  const canvasWidth = layout.canvasGridDimensions.cols * layout.gridSize;
  const canvasHeight = layout.canvasGridDimensions.rows * layout.gridSize;
  const bounds = getAreaBounds(area);

  if (bounds.right > canvasWidth || bounds.bottom > canvasHeight) {
    errors.push(`区域 "${area.name}" 超出画布边界`);
  }

  // 验证座位数据
  const expectedSeatCount = area.gridSize.rows * area.gridSize.cols;
  if (area.seats.length !== expectedSeatCount) {
    errors.push(`区域 "${area.name}" 座位数量不匹配，期望 ${expectedSeatCount}，实际 ${area.seats.length}`);
  }

  // 验证座位位置
  for (const seat of area.seats) {
    if (seat.areaId !== area.id) {
      errors.push(`座位 ${seat.seatNumber} 的区域ID不匹配`);
    }

    if (seat.row < 0 || seat.row >= area.gridSize.rows ||
        seat.col < 0 || seat.col >= area.gridSize.cols) {
      errors.push(`座位 ${seat.seatNumber} 的行列位置超出区域范围`);
    }
  }

  return errors;
}

/**
 * 验证区域移动
 */
export function validateAreaMove(
  area: SeatArea,
  newPosition: Position,
  allAreas: SeatArea[],
  canvasBounds: { width: number; height: number }
): AreaValidationResult {
  const bounds = getAreaBounds(area);
  const width = bounds.width;
  const height = bounds.height;

  // 检查边界
  if (newPosition.x < 0 || newPosition.y < 0) {
    return { valid: false, reason: '区域不能移动到画布边界外' };
  }

  if (newPosition.x + width > canvasBounds.width ||
      newPosition.y + height > canvasBounds.height) {
    return { valid: false, reason: '区域移动后会超出画布边界' };
  }

  // 检查与其他区域的重叠
  const tempArea = {
    ...area,
    position: { x: newPosition.x, y: newPosition.y },
  };

  const otherAreas = allAreas.filter(a => a.id !== area.id);
  if (checkAreaOverlap(tempArea, otherAreas)) {
    return { valid: false, reason: '区域移动后会与其他区域重叠' };
  }

  return { valid: true };
}

/**
 * 验证区域缩放
 */
export function validateAreaResize(
  area: SeatArea,
  direction: ResizeDirection,
  newGridSize: GridSize,
  allAreas: SeatArea[],
  canvasBounds: { width: number; height: number }
): AreaValidationResult {
  // 检查最小尺寸
  if (newGridSize.rows < 1 || newGridSize.cols < 1) {
    return { valid: false, reason: '区域尺寸不能小于1x1' };
  }

  // 创建临时区域用于验证
  const tempArea: SeatArea = {
    ...area,
    gridSize: { ...newGridSize },
  };

  const bounds = getAreaBounds(tempArea);

  // 检查边界
  if (bounds.right > canvasBounds.width || bounds.bottom > canvasBounds.height) {
    return { valid: false, reason: '缩放后区域会超出画布边界' };
  }

  // 检查与其他区域的重叠
  const otherAreas = allAreas.filter(a => a.id !== area.id);
  if (checkAreaOverlap(tempArea, otherAreas)) {
    return { valid: false, reason: '缩放后区域会与其他区域重叠' };
  }

  return { valid: true };
}

/**
 * 计算缩放时受影响的座位数量
 */
export function calculateAffectedSeats(
  area: SeatArea,
  newGridSize: GridSize
): number {
  let affectedCount = 0;

  // 检查超出新尺寸范围的座位
  for (const seat of area.seats) {
    if (seat.row >= newGridSize.rows || seat.col >= newGridSize.cols) {
      if (isSeatOccupied(seat)) {
        affectedCount++;
      }
    }
  }

  return affectedCount;
}

/**
 * 验证座位号格式
 */
export function validateSeatNumber(seatNumber: string): boolean {
  // 座位号格式：字母+数字，如 A01, B12
  const seatNumberRegex = /^[A-Z]\d{2}$/;
  return seatNumberRegex.test(seatNumber);
}

/**
 * 验证区域名称
 */
export function validateAreaName(name: string, existingAreas: SeatArea[], excludeId?: string): string[] {
  const errors: string[] = [];

  if (!name || name.trim().length === 0) {
    errors.push('区域名称不能为空');
    return errors;
  }

  if (name.length > 50) {
    errors.push('区域名称不能超过50个字符');
  }

  // 检查名称是否重复
  const duplicateArea = existingAreas.find(area => 
    area.name === name && area.id !== excludeId
  );
  
  if (duplicateArea) {
    errors.push('区域名称已存在');
  }

  return errors;
}
