/**
 * 区域操作相关工具函数
 * 处理区域的创建、查找、验证、移动等操作
 */

import type {
  Position,
  MutablePosition,
  Size,
  GridSize,
  MutableGridSize,
  SeatArea,
  AreaCreateConfig,
  AreaValidationResult,
  BoundingBox,
  AreaStyle,
} from '../types';
import { generateId, clonePosition, isPointInRect } from './coordinate';
import { generateSeatsForArea } from './seat';

/**
 * 默认区域样式
 */
export const DEFAULT_AREA_STYLE: AreaStyle = {
  color: '#f0f0f0',
  borderColor: '#d0d0d0',
};

/**
 * 默认座位间距
 */
export const DEFAULT_SEAT_SPACING: Size = {
  width: 40,
  height: 40,
};

/**
 * 生成默认区域名称
 */
export function generateDefaultAreaName(existingAreas: SeatArea[]): string {
  const nextNumber = existingAreas.length + 1;
  return `区域${nextNumber}`;
}

/**
 * 计算拖拽区域的网格尺寸
 */
export function calculateGridSize(startPos: Position, endPos: Position, seatSpacing: Size): GridSize {
  const startGridX = Math.floor(startPos.x / seatSpacing.width);
  const startGridY = Math.floor(startPos.y / seatSpacing.height);
  const endGridX = Math.floor(endPos.x / seatSpacing.width);
  const endGridY = Math.floor(endPos.y / seatSpacing.height);

  return {
    rows: Math.abs(endGridY - startGridY) + 1,
    cols: Math.abs(endGridX - startGridX) + 1,
  };
}

/**
 * 计算拖拽区域的起始位置
 */
export function calculateAreaPosition(startPos: Position, endPos: Position, seatSpacing: Size): Position {
  const startGridX = Math.floor(startPos.x / seatSpacing.width);
  const startGridY = Math.floor(startPos.y / seatSpacing.height);
  const endGridX = Math.floor(endPos.x / seatSpacing.width);
  const endGridY = Math.floor(endPos.y / seatSpacing.height);

  return {
    x: Math.min(startGridX, endGridX) * seatSpacing.width,
    y: Math.min(startGridY, endGridY) * seatSpacing.height,
  };
}

/**
 * 计算区域的边界框
 */
export function getAreaBounds(area: SeatArea): BoundingBox {
  const width = area.gridSize.cols * area.seatSpacing.width;
  const height = area.gridSize.rows * area.seatSpacing.height;

  return {
    left: area.position.x,
    top: area.position.y,
    right: area.position.x + width,
    bottom: area.position.y + height,
    width,
    height,
  };
}

/**
 * 检查两个区域是否重叠
 */
export function checkAreaOverlap(
  area1: { position: Position; gridSize: GridSize; seatSpacing: Size },
  areas: SeatArea[]
): boolean {
  const bounds1 = {
    left: area1.position.x,
    top: area1.position.y,
    right: area1.position.x + area1.gridSize.cols * area1.seatSpacing.width,
    bottom: area1.position.y + area1.gridSize.rows * area1.seatSpacing.height,
  };

  return areas.some(area2 => {
    const bounds2 = getAreaBounds(area2);

    return !(
      bounds1.right <= bounds2.left ||
      bounds1.left >= bounds2.right ||
      bounds1.bottom <= bounds2.top ||
      bounds1.top >= bounds2.bottom
    );
  });
}

/**
 * 根据位置查找区域
 */
export function findAreaAtPosition(position: Position, areas: SeatArea[]): SeatArea | null {
  return areas.find(area => {
    const bounds = getAreaBounds(area);
    return isPointInRect(position, {
      x: bounds.left,
      y: bounds.top,
      width: bounds.width,
      height: bounds.height,
    });
  }) || null;
}

/**
 * 创建座位区域
 */
export function createSeatArea(config: AreaCreateConfig): SeatArea {
  const areaId = generateId();
  const style = { ...DEFAULT_AREA_STYLE, ...config.style };

  const area: SeatArea = {
    id: areaId,
    name: config.name,
    position: clonePosition(config.position),
    gridSize: { ...config.gridSize },
    seatSpacing: DEFAULT_SEAT_SPACING,
    seats: [],
    style,
    createdAt: new Date().toISOString(),
  };

  // 生成座位
  area.seats = generateSeatsForArea(area);

  return area;
}

/**
 * 创建座位区域（向后兼容的重载版本）
 */
export function createSeatArea(
  name: string,
  position: Position,
  gridSize: GridSize,
): SeatArea;
export function createSeatArea(
  configOrName: AreaCreateConfig | string,
  position?: Position,
  gridSize?: GridSize,
): SeatArea {
  if (typeof configOrName === 'string') {
    // 向后兼容的调用方式
    const config: AreaCreateConfig = {
      name: configOrName,
      position: position!,
      gridSize: gridSize!,
    };
    return createSeatArea(config);
  } else {
    // 新的调用方式
    const areaId = generateId();
    const style = { ...DEFAULT_AREA_STYLE, ...configOrName.style };

    const area: SeatArea = {
      id: areaId,
      name: configOrName.name,
      position: clonePosition(configOrName.position),
      gridSize: { ...configOrName.gridSize },
      seatSpacing: DEFAULT_SEAT_SPACING,
      seats: [],
      style,
      createdAt: new Date().toISOString(),
    };

    // 生成座位
    area.seats = generateSeatsForArea(area);

    return area;
  }
}

/**
 * 验证区域放置是否有效
 */
export function validateAreaPlacement(
  position: Position,
  gridSize: GridSize,
  canvasBounds: { width: number; height: number },
  existingAreas: SeatArea[],
  excludeAreaId?: string
): AreaValidationResult {
  const areaWidth = gridSize.cols * DEFAULT_SEAT_SPACING.width;
  const areaHeight = gridSize.rows * DEFAULT_SEAT_SPACING.height;

  // 检查边界
  if (position.x < 0 || position.y < 0) {
    return { valid: false, reason: '区域不能超出画布边界' };
  }

  if (position.x + areaWidth > canvasBounds.width ||
      position.y + areaHeight > canvasBounds.height) {
    return { valid: false, reason: '区域超出画布边界' };
  }

  // 检查重叠
  const filteredAreas = excludeAreaId
    ? existingAreas.filter(area => area.id !== excludeAreaId)
    : existingAreas;

  const tempArea = {
    position,
    gridSize,
    seatSpacing: DEFAULT_SEAT_SPACING,
  };

  if (checkAreaOverlap(tempArea, filteredAreas)) {
    return { valid: false, reason: '区域与现有区域重叠' };
  }

  return { valid: true };
}
