<script lang="ts" setup>
import {
  ref,
  reactive,
  computed,
  onActivated,
  onDeactivated,
  useTemplateRef,
  nextTick,
  watch,
} from 'vue';
import { Message } from '@arco-design/web-vue';
import {
  IconSelectAll,
  IconPlus,
  IconDelete,
  IconMinus,
  IconApps,
  IconFullscreen,
  IconSave,
  IconEdit,
  IconClose,
} from '@arco-design/web-vue/es/icon';
import { useWindowSize } from '@vueuse/core';
import type {
  VenueLayout,
  EditorState,
  Tool,
  ResizeDirection,
} from './types';
import { CANVAS_CONFIG, ZOOM_CONFIG } from './constants';
import { validateLayout, exportLayoutData } from './utils';

import { useCanvas } from './hooks/use-canvas';
import { useAreaManager } from './hooks/use-area-manager';
import { useSeatEditor } from './hooks/use-seat-editor';

// 页面状态
const saving = ref(false);

// 画布引用
const canvasRef = useTemplateRef('canvasEl');
const canvasContainerRef = useTemplateRef('canvasContainerEl');
const nameInputRef = useTemplateRef('nameInputEl');
const mainRef = useTemplateRef('mainEl');

// 布局数据
const layout = ref<VenueLayout>({
  canvasGridDimensions: CANVAS_CONFIG.DEFAULT_GRID_DIMENSIONS,
  gridSize: CANVAS_CONFIG.GRID_SIZE,
  areas: [],
});

// 编辑器状态
const editorState = ref<EditorState>({
  currentTool: 'seat-area',
  selectedAreaId: undefined,
  selectedSeatId: undefined,
  scale: ZOOM_CONFIG.DEFAULT,
  isDrawing: false,
  showGrid: true,
  showAreaNames: true,
});

// 区域编辑对话框状态
const areaEditDialog = ref({
  visible: false,
  areaId: '',
  newName: '',
  originalName: '',
});

// 删除确认对话框状态
const deleteConfirmDialog = ref({
  visible: false,
  areaId: '',
  areaName: '',
  seatCount: 0,
});

// 拖拽状态
const dragState = reactive({
  isDragging: false,
  isCreatingArea: false,
  startPosition: { x: 0, y: 0 },
  currentPosition: { x: 0, y: 0 },
  dragType: 'none' as 'none' | 'area' | 'canvas' | 'resize',
  targetAreaId: '',
  startAreaPosition: { x: 0, y: 0 },
  spacePressed: false,
});

const { width, height } = useWindowSize();

// 画布操作
const {
  getMouseEventPosition,
  snapPositionToSeatGrid,
  zoomIn,
  zoomOut,
  zoomReset,
  fitToView,
  toggleGrid,
  scalePercentage,
  canvasTransform,
  canZoomIn,
  canZoomOut,
} = useCanvas(canvasRef, editorState);

// 区域管理
const {
  totalAreas,
  totalSeats,
  selectedArea,
  findAreaAt,
  findSeatAt,
  startCreateArea,
  selectArea,
  clearSelection,
  deleteArea,
  editArea,
  moveArea,
  resizeState,
  resizeConfirmDialog,
  currentResizeGridSize,
  startResize,
  updateResize,
  finishResize,
  confirmResize,
  cancelResizeConfirm,
} = useAreaManager(layout, editorState);

// 座位编辑
const {
  seatEditDialog,
  editingSeat,
  selectedSeat,
  selectedSeatArea,
  selectSeat,
  startEditSeat,
  closeSeatEditDialog,
  editingArea,
} = useSeatEditor(layout, editorState);

/**
 * 计算画布大小
 */
const computedCanvasSize = computed(() => {
  const { cols, rows } = layout.value.canvasGridDimensions;
  return {
    width: cols * CANVAS_CONFIG.GRID_SIZE,
    height: rows * CANVAS_CONFIG.GRID_SIZE,
  };
});

const canvasStyle = computed(() => ({
  ...canvasTransform.value,
  width: `${computedCanvasSize.value.width}px`,
  height: `${computedCanvasSize.value.height}px`,
}));

/**
 * 鼠标按下事件
 */
const handleMouseDown = (event: MouseEvent) => {
  if (!canvasRef.value || event.button !== 0) {
    return;
  }

  // 空格键模式下，开始画布拖拽
  if (dragState.spacePressed) {
    dragState.isDragging = true;
    dragState.dragType = 'canvas';
    dragState.startPosition = { x: event.clientX, y: event.clientY };

    if (canvasContainerRef.value) {
      dragState.currentPosition = {
        x: canvasContainerRef.value.scrollLeft,
        y: canvasContainerRef.value.scrollTop,
      };
    }
    return;
  }

  const position = getMouseEventPosition(event);

  // 检查是否点击在区域上
  const clickedArea = findAreaAt(position.canvas);
  const clickedSeat = findSeatAt(position.canvas);

  switch (editorState.value.currentTool) {
    case 'select':
      if (clickedSeat) {
        // 选中座位
        selectSeat(clickedSeat.area.id, clickedSeat.seat.id);
      } else if (clickedArea) {
        // 选中区域并开始拖拽
        selectArea(clickedArea.id);

        // 开始拖拽区域
        dragState.isDragging = true;
        dragState.dragType = 'area';
        dragState.targetAreaId = clickedArea.id;
        dragState.startPosition = { ...position.canvas };
        dragState.startAreaPosition = { ...clickedArea.position };
        dragState.currentPosition = { ...position.canvas };
      } else {
        // 清除选择并开始画布拖拽
        clearSelection();

        dragState.isDragging = true;
        dragState.dragType = 'canvas';
        dragState.startPosition = { x: event.clientX, y: event.clientY };

        if (canvasContainerRef.value) {
          dragState.currentPosition = {
            x: canvasContainerRef.value.scrollLeft,
            y: canvasContainerRef.value.scrollTop,
          };
        }
        editorState.value.isDrawing = false;
      }
      break;

    case 'seat-area':
      // 新增区域状态下的点击行为 - 也支持拖拽
      if (clickedSeat) {
        // 选中座位
        selectSeat(clickedSeat.area.id, clickedSeat.seat.id);
      } else if (clickedArea) {
        // 选中区域并开始拖拽
        selectArea(clickedArea.id);

        // 开始拖拽区域
        dragState.isDragging = true;
        dragState.dragType = 'area';
        dragState.targetAreaId = clickedArea.id;
        dragState.startPosition = { ...position.canvas };
        dragState.startAreaPosition = { ...clickedArea.position };
        dragState.currentPosition = { ...position.canvas };
      } else {
        // 开始创建区域
        dragState.isCreatingArea = true;
        // 使用座位网格吸附并限制在画布内
        const gridPos = snapPositionToSeatGrid(position.canvas);
        const maxX = computedCanvasSize.value.width - 40;
        const maxY = computedCanvasSize.value.height - 40;

        dragState.startPosition = {
          x: Math.max(0, Math.min(gridPos.x, maxX)),
          y: Math.max(0, Math.min(gridPos.y, maxY)),
        };
        dragState.currentPosition = dragState.startPosition;
        editorState.value.isDrawing = true;
      }
      break;

    case 'delete':
      if (clickedArea) {
        confirmDeleteArea(clickedArea.id);
      }
      break;

    default:
      break;
  }

  event.preventDefault();
};

/**
 * 鼠标移动事件
 */
const handleMouseMove = (event: MouseEvent) => {
  let position;
  let currentPos;
  let maxX;
  let maxY;
  let deltaX;
  let deltaY;

  if (dragState.isDragging) {
    switch (dragState.dragType) {
      case 'area':
        if (!canvasRef.value) { return; }
        position = getMouseEventPosition(event);
        handleAreaDragMove(position.canvas);
        break;
      case 'resize':
        if (!canvasRef.value) { return; }
        position = getMouseEventPosition(event);
        handleResizeDragMove(position.canvas);
        break;
      case 'canvas':
        // 画布拖拽 - 直接处理滚动
        deltaX = event.clientX - dragState.startPosition.x;
        deltaY = event.clientY - dragState.startPosition.y;

        if (canvasContainerRef.value) {
          canvasContainerRef.value.scrollLeft = dragState.currentPosition.x - deltaX;
          canvasContainerRef.value.scrollTop = dragState.currentPosition.y - deltaY;
        }
        break;
      default:
        break;
    }
  } else if (dragState.isCreatingArea) {
    if (!canvasRef.value) { return; }
    position = getMouseEventPosition(event);
    currentPos = snapPositionToSeatGrid(position.canvas);

    // 限制坐标在画布边界内
    maxX = computedCanvasSize.value.width - 40; // 减去最小区域宽度
    maxY = computedCanvasSize.value.height - 40; // 减去最小区域高度

    currentPos.x = Math.max(0, Math.min(currentPos.x, maxX));
    currentPos.y = Math.max(0, Math.min(currentPos.y, maxY));

    dragState.currentPosition = currentPos;
  }
};

/**
 * 处理区域拖拽移动
 */
const handleAreaDragMove = (currentCanvasPos: { x: number; y: number }) => {
  if (!dragState.targetAreaId) {
    return;
  }

  const area = layout.value.areas.find(a => a.id === dragState.targetAreaId);
  if (!area) {
    return;
  }

  // 计算移动偏移量
  const deltaX = currentCanvasPos.x - dragState.startPosition.x;
  const deltaY = currentCanvasPos.y - dragState.startPosition.y;

  // 计算新位置
  const newX = dragState.startAreaPosition.x + deltaX;
  const newY = dragState.startAreaPosition.y + deltaY;

  // 验证新位置
  const areaWidth = area.gridSize.cols * area.seatSpacing.width;
  const areaHeight = area.gridSize.rows * area.seatSpacing.height;

  // 网格吸附 - 将位置吸附到40px网格
  const gridSize = 40;
  const snappedX = Math.round(newX / gridSize) * gridSize;
  const snappedY = Math.round(newY / gridSize) * gridSize;

  // 限制在画布边界内
  const boundedX = Math.max(0, Math.min(snappedX, computedCanvasSize.value.width - areaWidth));
  const boundedY = Math.max(0, Math.min(snappedY, computedCanvasSize.value.height - areaHeight));

  // 临时更新位置用于预览
  area.position.x = boundedX;
  area.position.y = boundedY;
};

/**
 * 鼠标抬起事件
 * 处理区域拖拽结束和区域创建完成
 */
const handleMouseUp = () => {
  if (dragState.isCreatingArea) {
    try {
      // 确保坐标在画布边界内
      const boundedStartX = Math.max(0, Math.min(dragState.startPosition.x, computedCanvasSize.value.width - 40));
      const boundedStartY = Math.max(0, Math.min(dragState.startPosition.y, computedCanvasSize.value.height - 40));
      const boundedEndX = Math.max(0, Math.min(dragState.currentPosition.x, computedCanvasSize.value.width - 40));
      const boundedEndY = Math.max(0, Math.min(dragState.currentPosition.y, computedCanvasSize.value.height - 40));

      const boundedStartPos = { x: boundedStartX, y: boundedStartY };
      const boundedEndPos = { x: boundedEndX, y: boundedEndY };

      // 完成区域创建
      startCreateArea(boundedStartPos, boundedEndPos);
    } catch (error) {
      Message.error((error as Error).message);
    }
  } else if (dragState.isDragging && dragState.dragType === 'area') {
    // 处理区域拖拽结束
    handleAreaDragEnd();
  } else if (dragState.isDragging && dragState.dragType === 'resize') {
    // 处理缩放拖拽结束
    handleResizeDragEnd();
  }

  // 重置拖拽状态
  dragState.isCreatingArea = false;
  dragState.isDragging = false;
  dragState.dragType = 'none';
  dragState.targetAreaId = '';
  dragState.startPosition = { x: 0, y: 0 };
  dragState.currentPosition = { x: 0, y: 0 };
  dragState.startAreaPosition = { x: 0, y: 0 };

  editorState.value.isDrawing = false;
};

/**
 * 处理区域拖拽结束
 */
const handleAreaDragEnd = () => {
  if (!dragState.targetAreaId) {
    return;
  }

  const area = layout.value.areas.find(a => a.id === dragState.targetAreaId);
  if (!area) {
    return;
  }

  // 验证最终位置
  const isValid = moveArea(dragState.targetAreaId, area.position);
  if (!isValid) {
    // 如果位置无效，恢复到原始位置
    area.position.x = dragState.startAreaPosition.x;
    area.position.y = dragState.startAreaPosition.y;
  }
};

/**
 * 处理缩放拖拽移动
 */
const handleResizeDragMove = (currentCanvasPos: { x: number; y: number }) => {
  if (!resizeState.value.isResizing) {
    return;
  }

  updateResize(currentCanvasPos);
};

/**
 * 开始缩放
 */
const handleResizeStart = (areaId: string, direction: ResizeDirection, event: MouseEvent) => {
  event.stopPropagation();

  if (!canvasRef.value) { return; }

  const position = getMouseEventPosition(event);

  if (startResize(areaId, direction, position.canvas)) {
    dragState.isDragging = true;
    dragState.dragType = 'resize';
    dragState.targetAreaId = areaId;
    dragState.startPosition = { ...position.canvas };
    dragState.currentPosition = { ...position.canvas };
  }
};

/**
 * 处理缩放拖拽结束
 */
const handleResizeDragEnd = () => {
  if (!resizeState.value.isResizing) {
    return;
  }

  try {
    finishResize();
  } catch (error) {
    // 验证失败时重置缩放状态
    cancelResizeConfirm();
  }
};


/**
 * 切换工具
 */
const handleToolChange = (tool: Tool) => {
  editorState.value.currentTool = tool;
  clearSelection();
  dragState.isCreatingArea = false;
  editorState.value.isDrawing = false;
};

/**
 * 工具栏 - 编辑区域
 */
const handleEditAreaFromToolbar = (area: any) => {
  areaEditDialog.value = {
    visible: true,
    areaId: area.id,
    newName: area.name,
    originalName: area.name,
  };
};

/**
 * 工具栏 - 删除区域
 */
const handleDeleteAreaFromToolbar = (areaId: string) => {
  confirmDeleteArea(areaId);
};

/**
 * 工具栏 - 编辑座位
 */
const handleEditSeatFromToolbar = (areaId: string, seatId: string) => {
  startEditSeat(areaId, seatId);
};

/**
 * 工具栏 - 清空座位信息
 */
const handleClearSeatFromToolbar = (areaId: string, seatId: string) => {
  const areaIndex = layout.value.areas.findIndex(a => a.id === areaId);
  if (areaIndex === -1) {
    return;
  }

  const seatIndex = layout.value.areas[areaIndex].seats.findIndex(s => s.id === seatId);
  if (seatIndex === -1) {
    return;
  }

  layout.value.areas[areaIndex].seats[seatIndex] = {
    ...layout.value.areas[areaIndex].seats[seatIndex],
    name: undefined,
    phone: undefined,
  };

  Message.success('座位信息已清空');
};

/**
 * 计算区域工具栏位置
 */
const getAreaToolbarPosition = (area: any) => {
  const areaWidth = area.gridSize.cols * area.seatSpacing.width;
  return {
    left: area.position.x + areaWidth + 8 + 'px',
    top: area.position.y - 8 + 'px',
  };
};

/**
 * 计算座位工具栏位置
 */
const getSeatToolbarPosition = (seat: any, area: any) => {
  const seatAbsoluteX = area.position.x + seat.position.x;
  const seatAbsoluteY = area.position.y + seat.position.y;

  // 计算区域中心位置
  const areaWidth = area.gridSize.cols * area.seatSpacing.width;
  const areaCenterX = area.position.x + areaWidth / 2;

  // 通过DOM获取区域名称标签的实际宽度
  const labelElement = document.querySelector(`[data-area-label="${area.id}"]`) as HTMLElement;
  let labelTotalWidth = 120; // 默认宽度作为后备

  if (labelElement) {
    // 获取元素的实际宽度（包括padding、border等）
    labelTotalWidth = labelElement.offsetWidth;
  }

  // 计算标签的实际边界
  const labelLeft = areaCenterX - labelTotalWidth / 2;
  const labelRight = areaCenterX + labelTotalWidth / 2;
  const labelBottom = area.position.y - 5; // 标签下边界（考虑箭头）

  // 座位工具栏默认位置是座位上方45px
  const defaultToolbarTop = seatAbsoluteY - 45;

  // 检查工具栏是否会与区域名称标签重叠
  const willOverlapLabel = (
    seatAbsoluteX + 15 >= labelLeft && // 座位中心在标签范围内
    seatAbsoluteX + 15 <= labelRight &&
    defaultToolbarTop <= labelBottom && // 工具栏可能与标签重叠
    seatAbsoluteY >= area.position.y && // 座位在区域内
    seatAbsoluteY <= area.position.y + 60 // 座位在区域上方部分
  );

  if (willOverlapLabel) {
    // 如果会遮挡，则显示在座位右侧
    return {
      left: seatAbsoluteX + 45 + 'px', // 座位右侧
      top: seatAbsoluteY + 15 + 'px', // 座位中央
      transform: 'translateY(-50%)',
    };
  } else {
    // 默认显示在座位上方
    return {
      left: seatAbsoluteX + 15 + 'px', // 座位中心
      top: seatAbsoluteY - 45 + 'px', // 座位上方
      transform: 'translateX(-50%)',
    };
  }
};

/**
 * 确认编辑区域
 */
const handleConfirmEditArea = () => {
  try {
    editArea(areaEditDialog.value.areaId, areaEditDialog.value.newName);
    Message.success('区域编辑成功');
    areaEditDialog.value.visible = false;
  } catch (error) {
    Message.error((error as Error).message);
  }
};

/**
 * 取消编辑区域
 */
const handleCancelEditArea = () => {
  areaEditDialog.value.visible = false;
};

/**
 * 保存布局
 */
const handleSave = async () => {
  // 验证布局
  const errors = validateLayout(layout.value);
  if (errors.length > 0) {
    Message.error(`布局验证失败：${errors.join(', ')}`);
    return;
  }

  saving.value = true;
  try {
    console.log('保存布局数据:', layout.value);
    console.log('导出前台数据:', exportLayoutData(layout.value));
    console.log(JSON.stringify(exportLayoutData(layout.value), null, 2));

    await new Promise(resolve => setTimeout(resolve, 1000));

    Message.success('布局保存成功');
  } catch (error) {
    Message.error('保存失败');
    console.error(error);
  } finally {
    saving.value = false;
  }
};

/**
 * 保存座位信息并处理顺序编辑
 */
const handleSaveSeat = (event?: KeyboardEvent) => {
  if (!editingSeat.value || !editingArea.value) { return; }

  if (event) {
    event.preventDefault();
    event.stopPropagation();
  }

  // 保存当前座位信息到实际数据
  const areaIndex = layout.value.areas.findIndex(a => a.id === editingArea.value!.id);
  if (areaIndex === -1) { return; }

  const seatIndex = layout.value.areas[areaIndex].seats.findIndex(
    s => s.id === editingSeat.value!.id,
  );
  if (seatIndex === -1) { return; }

  // 更新实际座位数据
  const updatedSeat = {
    ...editingSeat.value,
    name: editingSeat.value.name?.trim() || undefined,
    phone: editingSeat.value.phone?.trim() || undefined,
  };

  layout.value.areas[areaIndex].seats.splice(seatIndex, 1, updatedSeat);

  // 查找当前区域的下一个座位
  const nextSeat = findNextSeatInArea(editingArea.value.id, editingSeat.value.id);

  if (nextSeat) {
    editingSeat.value = { ...nextSeat.seat };
    setTimeout(() => {
      nameInputRef.value?.focus();
    }, 50);
  } else {
    // 已经是最后一个座位，关闭对话框
    closeSeatEditDialog();
  }
};

/**
 * 查找区域内的下一个座位
 */
const findNextSeatInArea = (areaId: string, currentSeatId: string) => {
  const area = layout.value.areas.find(a => a.id === areaId);
  if (!area) { return null; }

  const currentIndex = area.seats.findIndex(seat => seat.id === currentSeatId);
  if (currentIndex === -1 || currentIndex >= area.seats.length - 1) {
    return null;
  }

  const nextSeat = area.seats[currentIndex + 1];
  return {
    areaId,
    seatId: nextSeat.id,
    seat: nextSeat,
  };
};

/**
 * 获取当前座位索引
 */
const getCurrentSeatIndex = () => {
  if (!editingArea.value || !editingSeat.value) { return 1; }

  const currentIndex = editingArea.value.seats.findIndex(seat => seat.id === editingSeat.value!.id);
  return currentIndex >= 0 ? currentIndex + 1 : 1;
};

/**
 * 获取座位总数
 */
const getTotalSeatsInArea = () => {
  if (!editingArea.value) { return 1; }
  return editingArea.value.seats.length;
};

/**
 * 处理确认前的操作
 */
const handleBeforeOk = () => {
  handleSaveSeat();
  return false;
};

/**
 * 取消座位编辑
 */
const handleCancelSeatEdit = () => {
  closeSeatEditDialog();
};

const isInputField = (element: HTMLElement) => {
  return ['INPUT', 'TEXTAREA', 'SELECT'].includes(element.tagName);
}

/**
 * 键盘快捷键
 */
const handleKeyDown = (event: KeyboardEvent) => {
  // 空格键 - 进入画布拖拽模式
  if (event.code === 'Space' && !isInputField(event.target as HTMLElement)) {
    event.preventDefault();

    if (!dragState.spacePressed && !event.repeat) {
      event.stopPropagation();
      dragState.spacePressed = true;
      return;
    }
  }

  // Delete键删除选中的区域或座位
  if (event.key === 'Delete') {
    if (editorState.value.selectedAreaId) {
      confirmDeleteSelectedArea();
    }
  }

  // Escape键取消操作
  if (event.key === 'Escape') {
    // 如果正在拖拽，取消拖拽并恢复位置
    if (dragState.isDragging && dragState.dragType === 'area') {
      const area = layout.value.areas.find(a => a.id === dragState.targetAreaId);
      if (area) {
        area.position.x = dragState.startAreaPosition.x;
        area.position.y = dragState.startAreaPosition.y;
      }
    }

    clearSelection();
    deleteConfirmDialog.value.visible = false;

    // 重置拖拽状态
    dragState.isDragging = false;
    dragState.isCreatingArea = false;
    dragState.dragType = 'none';
    dragState.targetAreaId = '';
    editorState.value.isDrawing = false;
  }
};

/**
 * 键盘释放事件
 */
const handleKeyUp = (event: KeyboardEvent) => {
  // 空格键释放 - 退出画布拖拽模式
  if (event.code === 'Space' && dragState.spacePressed) {
    event.preventDefault();
    dragState.spacePressed = false;

    // 如果正在空格拖拽，结束拖拽
    if (dragState.dragType === 'canvas') {
      dragState.isDragging = false;
      dragState.dragType = 'none';
    }
  }
};

/**
 * 确认删除区域
 */
const confirmDeleteArea = (areaId: string) => {
  const area = layout.value.areas.find(a => a.id === areaId);
  if (!area) {
    return;
  }

  deleteConfirmDialog.value = {
    visible: true,
    areaId,
    areaName: area.name,
    seatCount: area.seats.length,
  };
};

/**
 * 删除选中区域
 */
const confirmDeleteSelectedArea = () => {
  if (!editorState.value.selectedAreaId) {
    return;
  }
  confirmDeleteArea(editorState.value.selectedAreaId);
};

/**
 * 执行删除区域
 */
const handleConfirmDeleteArea = () => {
  try {
    const areaId = deleteConfirmDialog.value.areaId;
    deleteArea(areaId);
    Message.success('区域删除成功');
    deleteConfirmDialog.value.visible = false;
  } catch (error) {
    Message.error((error as Error).message);
  }
};

/**
 * 取消删除区域
 */
const handleCancelDeleteArea = () => {
  deleteConfirmDialog.value.visible = false;
};

/**
 * 确认缩放操作
 */
const handleConfirmResize = () => {
  try {
    confirmResize();
    Message.success('区域缩放成功');
  } catch (error) {
    Message.error((error as Error).message);
  }
};

/**
 * 取消缩放操作
 */
const handleCancelResize = () => {
  cancelResizeConfirm();
};

const handleFitToView = async () => {
  fitToView(computedCanvasSize.value);

  if (canvasContainerRef.value && canvasRef.value) {
    await nextTick();
    canvasContainerRef.value.scrollLeft = (canvasContainerRef.value.clientWidth - canvasRef.value.getBoundingClientRect().width) / 2;
  }
};

const handleZoomReset = async () => {
  zoomReset();

  if (canvasContainerRef.value && canvasRef.value) {
    await nextTick();
    canvasContainerRef.value.scrollLeft = Math.abs(canvasContainerRef.value.clientWidth - canvasRef.value.getBoundingClientRect().width - (CANVAS_CONFIG.EDITOR_PADDING.left + CANVAS_CONFIG.EDITOR_PADDING.right)) / 2;
  }
};

/**
 * 计算拖拽预览区域的行列数
 */
const previewAreaDimensions = computed(() => {
  if (!dragState.isCreatingArea) {
    return { cols: 0, rows: 0 };
  }

  const gridSize = 40;
  const width = Math.abs(dragState.currentPosition.x - dragState.startPosition.x) + gridSize;
  const height = Math.abs(dragState.currentPosition.y - dragState.startPosition.y) + gridSize;

  const cols = Math.max(1, Math.round(width / gridSize));
  const rows = Math.max(1, Math.round(height / gridSize));

  return { cols, rows };
});

/**
 * 计算缩放时的区域样式
 */
const getResizedAreaStyle = (area: any) => {
  if (!resizeState.value.isResizing || resizeState.value.targetAreaId !== area.id) {
    return {
      width: area.gridSize.cols * area.seatSpacing.width + 'px',
      height: area.gridSize.rows * area.seatSpacing.height + 'px',
    };
  }

  const newGridSize = currentResizeGridSize.value;
  return {
    width: newGridSize.cols * area.seatSpacing.width + 'px',
    height: newGridSize.rows * area.seatSpacing.height + 'px',
  };
};

/**
 * 计算画布鼠标样式
 */
const canvasCursor = computed(() => {
  if (dragState.isDragging) {
    return 'cursor-grabbing';
  }

  if (dragState.spacePressed) {
    return 'cursor-grab';
  }

  switch (editorState.value.currentTool) {
    case 'select':
      return 'cursor-default';
    case 'seat-area':
      return 'cursor-crosshair';
    case 'delete':
      return 'cursor-not-allowed';
    default:
      return 'cursor-default';
  }
});

const calculateHeight = () => {
  if (!mainRef.value) {
    return;
  }

  // 获取浏览器可视区域高度
  const viewportHeight = window.innerHeight;
  const rect = mainRef.value.getBoundingClientRect();

  mainRef.value.style.height = `${(viewportHeight - (rect.y + window.scrollY) - 40)}px`;
}

watch(
  () => [width.value, height.value],
  () => {
    calculateHeight();
  },
);

onActivated(() => {
  document.addEventListener('keydown', handleKeyDown);
  document.addEventListener('keyup', handleKeyUp);
  document.addEventListener('mouseup', handleMouseUp);

  setTimeout(() => {
    calculateHeight();
    handleFitToView();
  }, 50);
});

onDeactivated(() => {
  document.removeEventListener('keydown', handleKeyDown);
  document.removeEventListener('keyup', handleKeyUp);
  document.removeEventListener('mouseup', handleMouseUp);
});
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <div class="flex flex-col" ref="mainEl">
        <!-- 工具栏 -->
        <div class="flex flex-shrink-0 items-center justify-between gap-16px border-b border-gray-3 bg-white py-12px">
          <div class="flex items-center gap-12px">
            <!-- 工具选择 -->
            <AButtonGroup>
              <AButton
                :type="editorState.currentTool === 'select' ? 'primary' : 'secondary'"
                @click="handleToolChange('select')"
              >
                <template #icon>
                  <IconSelectAll />
                </template>
                选择
              </AButton>
              <AButton
                :type="editorState.currentTool === 'seat-area' ? 'primary' : 'secondary'"
                @click="handleToolChange('seat-area')"
              >
                <template #icon>
                  <IconPlus />
                </template>
                座位区域
              </AButton>
              <AButton
                :type="editorState.currentTool === 'delete' ? 'primary' : 'secondary'"
                @click="handleToolChange('delete')"
              >
                <template #icon>
                  <IconDelete />
                </template>
                删除
              </AButton>
            </AButtonGroup>

            <!-- 缩放控制 -->
            <AButtonGroup>
              <AButton @click="zoomOut" :disabled="!canZoomOut">
                <template #icon>
                  <IconMinus />
                </template>
              </AButton>
              <AButton @click="handleZoomReset">
                {{ scalePercentage }}%
              </AButton>
              <AButton @click="zoomIn" :disabled="!canZoomIn">
                <template #icon>
                  <IconPlus />
                </template>
              </AButton>
            </AButtonGroup>

            <!-- 视图控制 -->
            <AButtonGroup>
              <AButton @click="toggleGrid" :type="editorState.showGrid ? 'primary' : 'secondary'">
                <template #icon>
                  <IconApps />
                </template>
                网格
              </AButton>
              <AButton @click="handleFitToView">
                <template #icon>
                  <IconFullscreen />
                </template>
                适合窗口
              </AButton>
            </AButtonGroup>
          </div>

          <div class="flex items-center gap-12px">
            <!-- 统计信息 -->
            <span class="text-14px text-gray-6">
              区域: {{ totalAreas }} | 座位: {{ totalSeats }}
            </span>

            <!-- 操作按钮 -->
            <AButton type="primary" @click="handleSave" :loading="saving">
              <template #icon>
                <IconSave />
              </template>
              保存
            </AButton>
          </div>
        </div>

        <!-- 主舞台 -->
        <div class="flex flex-shrink-0 items-center bg-gray-2">
          <div class="h-50px w-full flex items-center justify-center text-24px font-bold">
            舞台
          </div>
        </div>

        <!-- 画布容器 -->
        <div
          ref="canvasContainerEl"
          class="relative flex-grow select-none overflow-auto bg-gray-2"
          :style="{
            paddingLeft: `${CANVAS_CONFIG.EDITOR_PADDING.left}px`,
            paddingRight: `${CANVAS_CONFIG.EDITOR_PADDING.right}px`,
            paddingTop: `${CANVAS_CONFIG.EDITOR_PADDING.top}px`,
            paddingBottom: `${CANVAS_CONFIG.EDITOR_PADDING.bottom}px`,
          }"
        >
          <div
            ref="canvasEl"
            class="relative border border-gray-4 bg-white"
            :style="canvasStyle"
            :class="canvasCursor"
            @mousedown="handleMouseDown"
            @mousemove="handleMouseMove"
          >
            <!-- 网格背景 -->
            <div
              v-if="editorState.showGrid"
              class="absolute inset-0 bg-[length:40px_40px] bg-[linear-gradient(to_right,#e5e5e5_1px,transparent_1px),linear-gradient(to_bottom,#e5e5e5_1px,transparent_1px)] opacity-30"
            >
            </div>

            <!-- 座位区域 -->
            <div
              v-for="area in layout.areas"
              :key="area.id"
              class="group absolute border-1px rounded bg-white bg-opacity-80 transition-all duration-200"
              :class="[
                editorState.selectedAreaId === area.id
                  ? 'border-blue-5 shadow-lg shadow-blue-5/20 ring-2 ring-blue-5/30'
                  : 'border-gray-5 border-solid hover:(border-blue-4 hover:shadow-md)',
                (editorState.currentTool === 'select' || editorState.currentTool === 'seat-area') && editorState.selectedAreaId === area.id
                  ? 'cursor-move hover:(cursor-grab) active:(cursor-grabbing)'
                  : 'cursor-default',
                dragState.isDragging && dragState.targetAreaId === area.id
                  ? `cursor-grabbing opacity-80 shadow-2xl ${dragState.dragType !== 'resize' ? 'scale-102' : ''} transition-none`
                  : ''
              ]"
              :style="{
                left: area.position.x + 'px',
                top: area.position.y + 'px',
                ...getResizedAreaStyle(area),
                backgroundColor: area.color,
                zIndex: dragState.isDragging && dragState.targetAreaId === area.id ? 1000 : 'auto',
              }"
              :title="area.name"
            >
              <!-- 区域标题 - 显示区域名称并支持下箭头 -->
              <div
                class="absolute left-1/2 z-100 transform whitespace-nowrap rounded bg-blue-5/90 px-8px py-4px text-12px text-white font-bold shadow-md after:(absolute left-50% top-100% size-0 transform-translate-x--1/2 border-width-5px border-transparent border-t-color-[rgba(59,130,246,0.9)] border-solid content-empty) -top-35px -translate-x-1/2"
                :class="{
                  'bg-blue-5/90': editorState.selectedAreaId === area.id,
                  'invisible': editorState.selectedAreaId !== area.id,
                  'group-hover:visible': editorState.selectedAreaId !== area.id,
                }"
                :data-area-label="area.id"
              >
                {{ area.name || '(无名称)' }}
              </div>

              <!-- 座位 -->
              <div
                v-for="seat in area.seats"
                :key="seat.id"
                class="js-seat-item absolute h-30px w-30px cursor-pointer border-1px rounded bg-white transition-all duration-200 hover:(border-blue-5 shadow-md)"
                :class="{
                  'bg-blue-1 border-blue-5 ring-2 ring-blue-5/40 border-solid': editorState.selectedSeatId === seat.id && !seat.name,
                  'text-white bg-green-5 border-green-5 border-solid': !!seat.name && editorState.selectedSeatId !== seat.id,
                  'text-white bg-green-5 border-green-5 ring-2 ring-blue-5/40 border-solid': !!seat.name && editorState.selectedSeatId === seat.id,
                  'border-gray-5 border-solid': !seat.name && editorState.selectedSeatId !== seat.id,
                }"
                :style="{
                  left: seat.position.x + 'px',
                  top: seat.position.y + 'px',
                }"
                :title="seat.name ? `${seat.seatNumber} - ${seat.name}` : seat.seatNumber"
                @dblclick="startEditSeat(seat.areaId, seat.id)"
              >
              </div>

              <!-- 缩放手柄 - 仅在选中状态且非缩放非拖拽时显示 -->
              <template v-if="editorState.selectedAreaId === area.id && !resizeState.isResizing && !dragState.isDragging">
                <!-- 右侧缩放手柄 -->
                <div
                  class="absolute right--2px top-1/2 h-16px w-4px transform cursor-ew-resize rounded bg-blue-5 opacity-80 transition-opacity -translate-y-1/2 hover:opacity-100"
                  @mousedown="handleResizeStart(area.id, 'right', $event)"
                  title="向右缩放"
                ></div>

                <!-- 下侧缩放手柄 -->
                <div
                  class="absolute bottom--2px left-1/2 h-4px w-16px transform cursor-ns-resize rounded bg-blue-5 opacity-80 transition-opacity -translate-x-1/2 hover:opacity-100"
                  @mousedown="handleResizeStart(area.id, 'bottom', $event)"
                  title="向下缩放"
                ></div>

                <!-- 右下角缩放手柄 -->
                <div
                  class="absolute bottom--2px right--2px h-8px w-8px cursor-se-resize rounded bg-blue-5 opacity-80 transition-opacity hover:opacity-100"
                  @mousedown="handleResizeStart(area.id, 'bottom-right', $event)"
                  title="向右下缩放"
                ></div>
              </template>

              <!-- 缩放时的尺寸显示 -->
              <div
                v-if="resizeState.isResizing && resizeState.targetAreaId === area.id"
                class="absolute left-0 whitespace-nowrap rounded-md bg-blue-6 px-12px py-4px text-12px text-white font-semibold shadow-lg -top-30px"
              >
                {{ currentResizeGridSize.rows }}行×{{ currentResizeGridSize.cols }}列
              </div>
            </div>

            <!-- 正在绘制的区域预览 -->
            <div
              v-if="dragState.isCreatingArea"
              class="pointer-events-none absolute border-2px border-blue-5 border-dashed bg-blue-5 bg-opacity-10"
              :style="{
                left: Math.min(
                  Math.max(0, dragState.startPosition.x),
                  Math.max(0, dragState.currentPosition.x)
                ) + 'px',
                top: Math.min(
                  Math.max(0, dragState.startPosition.y),
                  Math.max(0, dragState.currentPosition.y)
                ) + 'px',
                width: Math.abs(
                  Math.min(dragState.currentPosition.x, computedCanvasSize.width) -
                    Math.max(0, dragState.startPosition.x),
                ) + 40 + 'px',
                height: Math.abs(
                  Math.min(dragState.currentPosition.y, computedCanvasSize.height) -
                    Math.max(0, dragState.startPosition.y),
                ) + 40 + 'px',
              }"
            >
              <!-- 预览网格线，显示将要创建的座位布局 -->
              <div class="absolute inset-0 opacity-60" style="background-image: linear-gradient(to right, rgba(24, 144, 255, 0.3) 1px, transparent 1px), linear-gradient(to bottom, rgba(24, 144, 255, 0.3) 1px, transparent 1px); background-size: 40px 40px;"></div>

              <!-- 行列数显示标签 -->
              <div
                v-if="previewAreaDimensions.cols > 0 && previewAreaDimensions.rows > 0"
                class="absolute left-0 whitespace-nowrap rounded-md bg-blue-6 px-12px py-4px text-12px text-white font-semibold shadow-lg -top-30px"
              >
                {{ previewAreaDimensions.rows }}行×{{ previewAreaDimensions.cols }}列
              </div>
            </div>

            <!-- 区域浮动工具栏 -->
            <div
              v-if="editorState.selectedAreaId && selectedArea && !resizeState.isResizing && !dragState.isDragging"
              class="pointer-events-auto absolute z-1001 flex items-center gap-4px border border-gray-3 rounded-md bg-white px-8px py-4px shadow-lg"
              :style="getAreaToolbarPosition(selectedArea)"
              @mousedown.stop
              @click.stop
            >
              <AButton
                size="mini"
                type="text"
                @click="handleEditAreaFromToolbar(selectedArea)"
                title="编辑区域"
              >
                <template #icon>
                  <IconEdit />
                </template>
              </AButton>
              <AButton
                size="mini"
                type="text"
                status="danger"
                @click="handleDeleteAreaFromToolbar(selectedArea.id)"
                title="删除区域"
              >
                <template #icon>
                  <IconDelete />
                </template>
              </AButton>
            </div>

            <!-- 座位浮动工具栏 -->
            <div
              v-if="editorState.selectedSeatId && selectedSeat && selectedSeatArea && !resizeState.isResizing && !dragState.isDragging"
              class="pointer-events-auto absolute z-1001 flex items-center gap-4px border border-gray-3 rounded-md bg-white px-8px py-4px shadow-lg"
              :style="getSeatToolbarPosition(selectedSeat, selectedSeatArea)"
              @mousedown.stop
              @click.stop
            >
              <AButton
                size="mini"
                type="text"
                @click="handleEditSeatFromToolbar(selectedSeatArea.id, selectedSeat.id)"
                title="编辑座位信息"
              >
                <template #icon>
                  <IconEdit />
                </template>
              </AButton>
              <AButton
                v-if="selectedSeat.name || selectedSeat.phone"
                size="mini"
                type="text"
                status="warning"
                @click="handleClearSeatFromToolbar(selectedSeatArea.id, selectedSeat.id)"
                title="清空座位信息"
              >
                <template #icon>
                  <IconClose />
                </template>
              </AButton>
            </div>
          </div>
        </div>
      </div>

      <!-- 区域编辑对话框 -->
      <AModal
        v-model:visible="areaEditDialog.visible"
        title="编辑区域"
        @ok="handleConfirmEditArea"
        @cancel="handleCancelEditArea"
      >
        <AForm :model="{}" layout="vertical">
          <AFormItem label="区域名称">
            <AInput
              v-model="areaEditDialog.newName"
              :placeholder="areaEditDialog.originalName"
              @keyup.enter="handleConfirmEditArea"
            />
          </AFormItem>
        </AForm>
      </AModal>

      <!-- 删除确认对话框 -->
      <AModal
        v-model:visible="deleteConfirmDialog.visible"
        title="确认删除区域"
        @ok="handleConfirmDeleteArea"
        @cancel="handleCancelDeleteArea"
      >
        <div class="text-center">
          <div class="mb-16px text-16px text-gray-8">
            确定要删除区域 <strong class="text-red-6">{{ deleteConfirmDialog.areaName }}</strong> 吗？
          </div>
          <div class="text-14px text-gray-6">
            此操作将删除该区域及包含的 <strong class="text-blue-6">{{ deleteConfirmDialog.seatCount }}</strong> 个座位，且无法恢复。
          </div>
        </div>
      </AModal>

      <!-- 座位编辑对话框 -->
      <AModal
        v-model:visible="seatEditDialog"
        :title="`编辑座位信息 (${editingArea?.name} - 第 ${getCurrentSeatIndex()} / ${getTotalSeatsInArea()} 个)`"
        :on-before-ok="handleBeforeOk"
        @cancel="handleCancelSeatEdit"
        @open="nameInputRef?.focus()"
      >
        <AForm v-if="editingSeat" :model="{}" layout="vertical">
          <AFormItem label="座位号">
            {{ editingSeat.seatNumber }}
          </AFormItem>
          <AFormItem label="姓名">
            <AInput v-model="editingSeat.name" placeholder="请输入姓名" ref="nameInputEl" @keydown.enter="handleSaveSeat" />
          </AFormItem>
          <AFormItem label="电话">
            <AInput v-model="editingSeat.phone" placeholder="请输入电话（选填）" @keydown.enter="handleSaveSeat" />
          </AFormItem>
          <div class="text-center text-12px text-gray-6">
            {{ getCurrentSeatIndex() === getTotalSeatsInArea() ? '这是最后一个座位，编辑完成后将关闭对话框' : '编辑完成后将自动切换到下一个座位' }}
          </div>
        </AForm>
      </AModal>

      <!-- 缩放确认对话框 -->
      <AModal
        v-model:visible="resizeConfirmDialog.visible"
        title="确认缩放区域"
        @ok="handleConfirmResize"
        @cancel="handleCancelResize"
      >
        <div class="text-center">
          <div class="mb-16px text-16px text-gray-8">
            缩放区域 <strong class="text-blue-6">{{ resizeConfirmDialog.areaName }}</strong> 将删除 <strong class="text-red-6">{{ resizeConfirmDialog.affectedSeats }}</strong> 个已填写信息的座位
          </div>
          <div class="text-14px text-gray-6">
            新尺寸: {{ resizeConfirmDialog.newGridSize.rows }}行 × {{ resizeConfirmDialog.newGridSize.cols }}列
          </div>
          <div class="mt-16px text-14px text-red-6 font-medium">
            此操作无法恢复，确定要继续吗？
          </div>
        </div>
      </AModal>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
