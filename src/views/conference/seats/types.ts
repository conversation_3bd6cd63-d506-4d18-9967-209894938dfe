/**
 * 座位编排功能的类型定义
 * 统一管理所有类型，便于维护
 */

// ============ 基础类型 ============

/** 二维坐标位置 */
export interface Position {
  x: number;
  y: number;
}

/** 尺寸 */
export interface Size {
  width: number;
  height: number;
}

/** 网格尺寸 */
export interface GridSize {
  rows: number;    // 行数
  cols: number;    // 列数
}

// ============ 座位相关类型 ============


/** 座位信息 */
export interface Seat {
  id: string;
  areaId: string;        // 所属区域ID
  position: Position;    // 在区域内的相对位置
  row: number;           // 在区域内的行号（0开始）
  col: number;           // 在区域内的列号（0开始）
  seatNumber: string;    // 座位号（如 "VIP-A1", "A区-01"）
  name?: string;         // 预约人姓名
  phone?: string;        // 预约人手机号（选填）
}


// ============ 区域相关类型 ============

/** 座位区域 */
export interface SeatArea {
  id: string;
  name: string;          // 区域名称（如 "VIP-A区", "主会场A区"）
  position: Position;    // 区域左上角位置（画布坐标）
  gridSize: GridSize;    // 网格尺寸（行数x列数）
  seatSpacing: Size;     // 座位间距（像素）
  seats: Seat[];         // 该区域的所有座位
  color: string;         // 区域背景颜色
  borderColor: string;   // 区域边框颜色
  createdAt?: string;
  updatedAt?: string;
}


// ============ 布局相关类型 ============

/** 会场布局 */
export interface VenueLayout {
  canvasGridDimensions: GridSize;  // 画布行列格子数
  gridSize: number;      // 网格单元大小（像素）
  areas: SeatArea[];     // 座位区域列表
}

// ============ 编辑器相关类型 ============

/** 工具类型 */
export type Tool = 'select' | 'seat-area' | 'delete';

/** 编辑器状态 */
export interface EditorState {
  currentTool: Tool;
  selectedAreaId?: string;
  selectedSeatId?: string;
  scale: number;         // 缩放比例
  isDrawing: boolean;    // 是否正在绘制
  showGrid: boolean;     // 是否显示网格
  showAreaNames: boolean; // 是否显示区域名称
}


// ============ 对话框相关类型 ============

/** 区域配置对话框状态 */
export interface AreaConfigDialog {
  visible: boolean;
  position: Position;
  gridSize: GridSize;
  defaultName: string;    // 默认名称如"区域1"、"区域2"
  selectedName: string;   // 用户输入的名称
}

// ============ 搜索相关类型 ============

/** 座位搜索结果 */
export interface SeatSearchResult {
  seat: Seat;
  area: SeatArea;
  absolutePosition: Position; // 座位的绝对位置
}

// ============ 导出相关类型 ============

/** 导出数据格式（用于前台展示） */
export interface ExportLayoutData {
  seats: (SimpleSeatData | null)[][];  // 二维座位表，null表示空座位
  areas: SimpleAreaInfo[];             // 区域信息列表
  totalRows: number;                   // 总行数
  totalCols: number;                   // 总列数
}

/** 简化的座位导出数据 */
export interface SimpleSeatData {
  name?: string;         // 用户姓名
  phone?: string;        // 用户电话
  seatNumber: string;    // 座位号
  areaName: string;      // 所属区域名称
}

/** 简化的区域信息 */
export interface SimpleAreaInfo {
  name: string;          // 区域名称
  startRow: number;      // 起始行（在简化表格中的位置）
  startCol: number;      // 起始列（在简化表格中的位置）
  rows: number;          // 行数
  cols: number;          // 列数
}


// ============ 缩放相关类型 ============

/** 缩放方向 */
export type ResizeDirection = 'right' | 'bottom' | 'bottom-right';

/** 缩放状态 */
export interface ResizeState {
  isResizing: boolean;
  direction: ResizeDirection | null;
  targetAreaId: string;
  startPosition: Position;
  currentPosition: Position;
  originalGridSize: GridSize;
}

/** 缩放确认对话框状态 */
export interface ResizeConfirmDialog {
  visible: boolean;
  areaId: string;
  areaName: string;
  direction: ResizeDirection;
  newGridSize: GridSize;
  affectedSeats: number; // 将被删除的已填写信息的座位数量
}

// ============ 事件相关类型 ============

/** 鼠标事件位置 */
export interface MouseEventPosition {
  canvas: Position;      // 画布坐标
  grid: Position;        // 网格坐标
  screen: Position;      // 屏幕坐标
}


