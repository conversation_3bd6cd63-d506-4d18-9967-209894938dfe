/**
 * 座位编排功能的类型定义
 * 统一管理所有类型，便于维护和类型安全
 */

// ============ 基础类型 ============

/** 二维坐标位置 */
export interface Position {
  readonly x: number;
  readonly y: number;
}

/** 可变的二维坐标位置 */
export interface MutablePosition {
  x: number;
  y: number;
}

/** 尺寸 */
export interface Size {
  readonly width: number;
  readonly height: number;
}

/** 网格尺寸 */
export interface GridSize {
  readonly rows: number;    // 行数
  readonly cols: number;    // 列数
}

/** 可变的网格尺寸 */
export interface MutableGridSize {
  rows: number;    // 行数
  cols: number;    // 列数
}

/** 边界框 */
export interface BoundingBox {
  readonly left: number;
  readonly top: number;
  readonly right: number;
  readonly bottom: number;
  readonly width: number;
  readonly height: number;
}

// ============ 座位相关类型 ============

/** 座位状态 */
export type SeatStatus = 'available' | 'occupied' | 'selected';

/** 座位信息 */
export interface Seat {
  readonly id: string;
  readonly areaId: string;        // 所属区域ID
  readonly position: MutablePosition;    // 在区域内的相对位置
  readonly row: number;           // 在区域内的行号（0开始）
  readonly col: number;           // 在区域内的列号（0开始）
  readonly seatNumber: string;    // 座位号（如 "VIP-A1", "A区-01"）
  name?: string;                  // 预约人姓名
  phone?: string;                 // 预约人手机号（选填）
}

/** 座位编辑信息 */
export interface SeatEditInfo {
  name?: string;
  phone?: string;
}

/** 座位查找结果 */
export interface SeatFindResult {
  readonly seat: Seat;
  readonly area: SeatArea;
}


// ============ 区域相关类型 ============

/** 区域样式配置 */
export interface AreaStyle {
  readonly color: string;         // 区域背景颜色
  readonly borderColor: string;   // 区域边框颜色
}

/** 座位区域 */
export interface SeatArea {
  readonly id: string;
  name: string;                   // 区域名称（如 "VIP-A区", "主会场A区"）
  readonly position: MutablePosition;    // 区域左上角位置（画布坐标）
  readonly gridSize: MutableGridSize;    // 网格尺寸（行数x列数）
  readonly seatSpacing: Size;     // 座位间距（像素）
  readonly seats: Seat[];         // 该区域的所有座位
  readonly style?: AreaStyle;     // 区域样式（可选，为了向后兼容）
  // 向后兼容的字段
  readonly color?: string;        // 区域背景颜色（已废弃，使用style.color）
  readonly borderColor?: string;  // 区域边框颜色（已废弃，使用style.borderColor）
  readonly createdAt?: string;
  readonly updatedAt?: string;
}

/** 区域创建配置 */
export interface AreaCreateConfig {
  readonly name: string;
  readonly position: Position;
  readonly gridSize: GridSize;
  readonly style?: Partial<AreaStyle>;
}

/** 区域验证结果 */
export interface AreaValidationResult {
  readonly valid: boolean;
  readonly reason?: string;
}


// ============ 布局相关类型 ============

/** 会场布局 */
export interface VenueLayout {
  canvasGridDimensions: GridSize;  // 画布行列格子数
  gridSize: number;      // 网格单元大小（像素）
  areas: SeatArea[];     // 座位区域列表
}

// ============ 编辑器相关类型 ============

/** 工具类型 */
export type Tool = 'select' | 'seat-area' | 'delete';

/** 编辑器状态 */
export interface EditorState {
  currentTool: Tool;
  selectedAreaId?: string;
  selectedSeatId?: string;
  scale: number;         // 缩放比例
  isDrawing: boolean;    // 是否正在绘制
  showGrid: boolean;     // 是否显示网格
  showAreaNames: boolean; // 是否显示区域名称
}

/** 拖拽类型 */
export type DragType = 'none' | 'area' | 'canvas' | 'resize';

/** 拖拽状态 */
export interface DragState {
  isDragging: boolean;
  isCreatingArea: boolean;
  startPosition: MutablePosition;
  currentPosition: MutablePosition;
  dragType: DragType;
  targetAreaId: string;
  startAreaPosition: MutablePosition;
  spacePressed: boolean;
}


// ============ 对话框相关类型 ============

/** 对话框基础状态 */
export interface BaseDialogState {
  visible: boolean;
}

/** 区域编辑对话框状态 */
export interface AreaEditDialogState extends BaseDialogState {
  areaId: string;
  newName: string;
  originalName: string;
}

/** 座位编辑对话框状态 */
export type SeatEditDialogState = BaseDialogState;

/** 删除确认对话框状态 */
export interface DeleteConfirmDialogState extends BaseDialogState {
  areaId: string;
  areaName: string;
  seatCount: number;
}

/** 区域配置对话框状态 */
export interface AreaConfigDialog {
  visible: boolean;
  position: Position;
  gridSize: GridSize;
  defaultName: string;    // 默认名称如"区域1"、"区域2"
  selectedName: string;   // 用户输入的名称
}

// ============ 搜索相关类型 ============

/** 座位搜索结果 */
export interface SeatSearchResult {
  seat: Seat;
  area: SeatArea;
  absolutePosition: Position; // 座位的绝对位置
}

// 导出相关类型已在下面定义，这里不重复

// ============ 导出相关类型 ============

/** 导出数据格式（用于前台展示） */
export interface ExportLayoutData {
  seats: (SimpleSeatData | null)[][];  // 二维座位表，null表示空座位
  areas: SimpleAreaInfo[];             // 区域信息列表
  totalRows: number;                   // 总行数
  totalCols: number;                   // 总列数
}

/** 简化的座位导出数据 */
export interface SimpleSeatData {
  name?: string;         // 用户姓名
  phone?: string;        // 用户电话
  seatNumber: string;    // 座位号
  areaName: string;      // 所属区域名称
}

/** 简化的区域信息 */
export interface SimpleAreaInfo {
  name: string;          // 区域名称
  startRow: number;      // 起始行（在简化表格中的位置）
  startCol: number;      // 起始列（在简化表格中的位置）
  rows: number;          // 行数
  cols: number;          // 列数
}


// ============ 缩放相关类型 ============

/** 缩放方向 */
export type ResizeDirection = 'right' | 'bottom' | 'bottom-right';

/** 缩放状态 */
export interface ResizeState {
  isResizing: boolean;
  direction: ResizeDirection | null;
  targetAreaId: string;
  startPosition: Position;
  currentPosition: Position;
  originalGridSize: GridSize;
}

/** 缩放确认对话框状态 */
export interface ResizeConfirmDialog {
  visible: boolean;
  areaId: string;
  areaName: string;
  direction: ResizeDirection;
  newGridSize: GridSize;
  affectedSeats: number; // 将被删除的已填写信息的座位数量
}

// ============ 事件相关类型 ============

/** 鼠标事件位置 */
export interface MouseEventPosition {
  readonly canvas: Position;      // 画布坐标
  readonly grid: Position;        // 网格坐标
  readonly screen: Position;      // 屏幕坐标
}

// ============ 事件处理相关类型 ============

/** 键盘事件处理器 */
export interface KeyboardEventHandler {
  (event: KeyboardEvent): void;
}

/** 鼠标事件处理器 */
export interface MouseEventHandler {
  (event: MouseEvent): void;
}

/** 工具栏操作类型 */
export type ToolbarAction =
  | 'edit-area'
  | 'delete-area'
  | 'edit-seat'
  | 'clear-seat';

/** 工具栏操作参数 */
export interface ToolbarActionParams {
  areaId?: string;
  seatId?: string;
  area?: SeatArea;
  seat?: Seat;
}
