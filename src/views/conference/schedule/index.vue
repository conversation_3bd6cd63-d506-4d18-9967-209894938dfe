<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success } = useNotification();
const forum = ref<Record<string, any>[]>([]);

const form = ref({
  name: '',
  forumId: Number(props.id) || '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/agenda/page', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      name: form.value.name,
      forumId: form.value.forumId,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/meeting/v1/agenda/remove', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const getForum = async () => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/forum/list', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
    },
  });

  if (isSuccess) {
    forum.value = data;
  }
}

onActivated(() => {
  getForum();
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="名称">
          <AInput v-model="form.name" allow-clear />
        </AFormItem>
        <AFormItem label="所属论坛">
          <ASelect v-model="form.forumId" :fallback-option="false">
            <AOption value="">全部</AOption>
            <AOption v-for="item in forum" :key="item.id" :value="item.id">{{ item.name }}</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton :to="`/conference/schedule/add/${props.id}`" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="主题" data-index="name" />

        <ATableColumn title="议程时间">
          <template #cell="{ record }">
            {{ record.beginTime }} - {{ record.endTime }}
          </template>
        </ATableColumn>

        <ATableColumn title="论坛" data-index="forumName" />

        <ATableColumn title="主讲人" data-index="speakerName" />
        <ATableColumn title="主讲人职位" data-index="speakerJob" />

        <ATableColumn title="主讲人照片" header-cell-class="w-10%">
          <template #cell="{ record }">
            <OpenImage :src="record.speakerPic" img-class="w-full h-100px object-contain object-left" />
          </template>
        </ATableColumn>

        <ATableColumn title="更新时间" data-index="updatedAt" :width="180" />

        <ATableColumn title="操作" :width="180">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/conference/schedule/edit/${record.id}/${props.id}`">编辑</OpenRouterButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.id)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
