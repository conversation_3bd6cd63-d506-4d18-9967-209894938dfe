<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { marked } from 'marked';
import MarkdownEditor from '@/components/markdown-editor/index.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  // 论坛ID
  fid: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref<Record<string, any>>({
  forumId: Number(props.fid),
  time: [],
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();
const forum = ref<Record<string, any>[]>([]);

marked.use({
  gfm: true,
  breaks: true,
});

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const data: Record<string, any> = {
    forumId: formData.value.forumId,
    name: formData.value.name,
    beginTime: formData.value.time[0],
    endTime: formData.value.time[1],
    address: formData.value.address,
    description: formData.value.description,
    speakerName: formData.value.speakerName,
    speakerJob: formData.value.speakerJob,
    speakerPic: formData.value.speakerPic,
  };

  const { isSuccess } = await $fetch(props.id ? '/admin/meeting/v1/agenda/update' : '/admin/meeting/v1/agenda/add', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id: props.id,
      ...data,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push(`/conference/schedule/index/${data.forumId}`);
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/agenda/detail', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      forumId: data.forumId,
      name: data.name,
      description: data.description,
      time: [data.beginTime, data.endTime],
      // address: data.address,
      speakerName: data.speakerName,
      speakerJob: data.speakerJob,
      speakerPic: data.speakerPic,
    };
  }
}

const getForum = async () => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/forum/list', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
    },
  });

  if (isSuccess) {
    forum.value = data;
  }
}

const markdownValidator = (value: string, callback: (error?: string) => void) => {
  const introText = (marked.parse(value || '') as string).replace(/<[^>]+>/g, ''); // 去除 HTML 标签
  if (introText.length > 500) {
    callback('不能超过 500 个字');
  }
  else {
    callback();
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }

  getForum();
}

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <OpenLoadingProvider v-slot="{ loading: componentLoading }">
        <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit">
          <AFormItem label="所属论坛" field="forumId" :rules="[{ required: true, message: '此项必选' }]">
            <ASelect v-model="formData.forumId" placeholder="请选择" :fallback-option="false">
              <AOption v-for="item in forum" :key="item.id" :value="item.id">{{ item.name }}</AOption>
            </ASelect>
          </AFormItem>

          <AFormItem label="主题" field="name" :rules="[{ required: true, message: '此项必填' }]">
            <AInput v-model="formData.name" />
          </AFormItem>

          <AFormItem label="描述" field="description" :rules="[{ validator: markdownValidator }]" help="Markdown 格式">
            <MarkdownEditor :height="300" v-model="formData.description" break ref="editor" />
          </AFormItem>

          <AFormItem label="时间" field="time" :rules="[{ required: true, message: '此项必填' }]">
            <ATimePicker type="time-range" format="HH:mm" v-model="formData.time" disable-confirm />
          </AFormItem>

          <!-- <AFormItem label="地点" field="address" :rules="[{ required: true, message: '此项必填' }]">
          <AInput v-model="formData.address" />
        </AFormItem> -->

          <AFormItem label="主讲人名称" field="speakerName">
            <AInput v-model="formData.speakerName" />
          </AFormItem>

          <AFormItem label="主讲人职位" field="speakerJob">
            <AInput v-model="formData.speakerJob" />
          </AFormItem>

          <AFormItem label="主讲人照片" field="speakerPic" help="图片尺寸: 171x168 像素">
            <OpenUpload v-model="formData.speakerPic" :image-aspect-ratio="171 / 168" />
          </AFormItem>

          <ADivider />

          <AFormItem>
            <ASpace>
              <OpenRouterButton :to="`/conference/schedule/index/${props.fid}`">返回列表</OpenRouterButton>
              <AButton html-type="submit" type="primary" :loading="loading || componentLoading">提交</AButton>
            </ASpace>
          </AFormItem>
        </AForm>
      </OpenLoadingProvider>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
