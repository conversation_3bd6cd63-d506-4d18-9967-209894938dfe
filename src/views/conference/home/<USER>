<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success } = useNotification();

const conferenceCode = import.meta.env.VITE_ENABLED_CONFERENCE_CODE;

const form = ref({
  name: '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/meet/page', {
    data: {
      name: form.value.name,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/meeting/v1/meet/remove', {
    data: {
      id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

onActivated(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="名称">
          <AInput v-model="form.name" allow-clear />
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/conference/home/<USER>" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="唯一标识" data-index="code" :width="200" />
        <ATableColumn title="名称" data-index="name" />

        <ATableColumn title="状态" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.code === conferenceCode" color="blue">启用</ATag>
            <ATag v-else>失效</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="创建时间" data-index="createdAt" :width="180" />
        <ATableColumn title="更新时间" data-index="updatedAt" :width="180" />

        <ATableColumn title="操作" :width="180">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/conference/home/<USER>/${record.id}`">编辑</OpenRouterButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.id)" v-confirm disabled>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
