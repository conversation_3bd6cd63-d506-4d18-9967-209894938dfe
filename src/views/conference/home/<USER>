<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import MarkdownEditor from '@/components/markdown-editor/index.vue';
import { marked } from 'marked';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },

  pageType: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref<Record<string, any>>({
  code: '',
  name: '',
  subject: '',
  subName: '',
  description: '',
  pic: '',
  guideUrl: '',
  extInfo: {},
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();
const editor = ref();

marked.use({
  gfm: true,
  breaks: true,
});

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const requestData: Record<string, any> = {};

  if (props.pageType === 'detail') {
    requestData.code = import.meta.env.VITE_ENABLED_CONFERENCE_CODE;
  }
  else {
    requestData.id = props.id;
  }

  const { isSuccess } = await $fetch((props.id || props.pageType === 'detail') ? '/admin/meeting/v1/meet/update' : '/admin/meeting/v1/meet/add', {
    data: {
      ...requestData,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');

    if (props.pageType === 'detail') {
      getDetail();
    }
    else {
      router.push('/conference/home/<USER>');
    }
  }
};

const getDetail = async () => {
  const requestData: Record<string, any> = {};

  if (props.pageType === 'detail') {
    requestData.code = import.meta.env.VITE_ENABLED_CONFERENCE_CODE;
  }
  else {
    requestData.id = props.id;
  }

  const { isSuccess, data } = await $fetch('/admin/meeting/v1/meet/detail', {
    data: requestData,
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      code: data.code,
      name: data.name,
      subject: data.subject,
      subName: data.subName,
      description: data.description,
      pic: data.pic,
      guideUrl: data.guideUrl,
      extInfo: data.extInfo,
    };

    editor.value?.scrollToTop();
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

init();

const markdownValidator = (value: string, callback: (error?: string) => void) => {
  const introText = (marked.parse(value || '') as string).replace(/<[^>]+>/g, ''); // 去除 HTML 标签
  if (introText.length > 500) {
    callback('不能超过 500 个字');
  }
  else {
    callback();
  }
}

onActivated(() => {
  getDetail();
});
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <OpenLoadingProvider v-slot="{ loading: componentLoading }">
        <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" v-confirm @submit="submit">
          <AFormItem label="唯一标识" field="code" :rules="[{ required: true, message: '此项必填' }]" v-if="pageType !== 'detail'">
            <AInput v-model="formData.code" />
          </AFormItem>

          <AFormItem label="名称" field="name" :rules="[{ required: true, message: '此项必填' }]">
            <AInput v-model="formData.name" />
          </AFormItem>

          <AFormItem label="标题" field="subject" :rules="[{ required: true, message: '此项必填' }]">
            <AInput v-model="formData.subject" />
          </AFormItem>

          <AFormItem label="小标题" field="subName">
            <AInput v-model="formData.subName" />
          </AFormItem>

          <AFormItem label="描述" field="description" :rules="[{ required: true, message: '此项必填' }, { validator: markdownValidator }]" help="Markdown 格式">
            <MarkdownEditor :height="300" v-model="formData.description" :toolbar="[['bold', 'italic', 'strike', 'link']]" break ref="editor" />
          </AFormItem>

          <AFormItem label="主题图" field="pic" :rules="[{ required: true, message: '此项必传' }]" help="图片尺寸: 636x456 像素">
            <OpenUpload v-model="formData.pic" :image-aspect-ratio="636 / 456" />
          </AFormItem>

          <AFormItem label="大会手册" field="guideUrl" help="文档格式: PDF">
            <OpenUpload v-model="formData.guideUrl" accept=".pdf" list-type="text" :max-file-size="1000 * 1000 * 90" />
          </AFormItem>

          <AFormItem label="开幕式数量" field="extInfo.openingCount" help="请输入整数，需要的话可以在整数结尾添加“+”">
            <AInput v-model="formData.extInfo.openingCount" />
          </AFormItem>

          <AFormItem label="分论坛数量" field="extInfo.forumCount" help="请输入整数，需要的话可以在整数结尾添加“+”">
            <AInput v-model="formData.extInfo.forumCount" />
          </AFormItem>

          <AFormItem label="精彩演讲数量" field="extInfo.scheduleCount" help="请输入整数，需要的话可以在整数结尾添加“+”">
            <AInput v-model="formData.extInfo.scheduleCount" />
          </AFormItem>

          <AFormItem label="参会单位数量" field="extInfo.unitCount" help="请输入整数，需要的话可以在整数结尾添加“+”">
            <AInput v-model="formData.extInfo.unitCount" />
          </AFormItem>

          <AFormItem label="联系我们邮箱" field="extInfo.email">
            <AInput v-model="formData.extInfo.email" />
          </AFormItem>

          <AFormItem label="联系我们地点" field="extInfo.address">
            <AInput v-model="formData.extInfo.address" />
          </AFormItem>

          <ADivider />

          <AFormItem>
            <ASpace>
              <OpenRouterButton to="/conference/home/<USER>" v-if="props.pageType !== 'detail'">返回列表</OpenRouterButton>
              <AButton html-type="submit" type="primary" :loading="loading || componentLoading">提交</AButton>
            </ASpace>
          </AFormItem>
        </AForm>
      </OpenLoadingProvider>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
