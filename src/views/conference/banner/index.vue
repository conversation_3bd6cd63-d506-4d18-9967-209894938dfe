<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { bannerCategory } from '../constants';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success } = useNotification();

const form = ref({
  name: '',
  status: 0,
  bannerType: '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/banner/page', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      name: form.value.name,
      status: form.value.status,
      bannerType: form.value.bannerType,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/meeting/v1/banner/remove', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const changeStatus = async (status: number, id: string) => {
  const { isSuccess } = await $fetch('/admin/meeting/v1/banner/onOrOff', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id,
      status,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    search.value.refresh();
  }
};

onActivated(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="名称">
          <AInput v-model="form.name" allow-clear />
        </AFormItem>

        <AFormItem label="位置">
          <ASelect v-model="form.bannerType">
            <AOption value="">全部</AOption>
            <AOption v-for="(item, key) in bannerCategory" :value="key" :key="key">{{ item.text }}</AOption>
          </ASelect>
        </AFormItem>

        <AFormItem label="状态">
          <ASelect v-model="form.status">
            <AOption :value="0">全部</AOption>
            <AOption :value="2">已发布</AOption>
            <AOption :value="1">已下线</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/conference/banner/add" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="位置" :width="200">
          <template #cell="{ record }">
            {{ bannerCategory[record.bannerType].text }}
          </template>
        </ATableColumn>

        <ATableColumn title="图片" header-cell-class="w-80">
          <template #cell="{ record }">
            <OpenImage :src="record.pic" img-class="w-full h-100px object-contain object-left" />
          </template>
        </ATableColumn>

        <ATableColumn title="标题" data-index="name" />
        <ATableColumn title="链接" data-index="link" />

        <ATableColumn title="状态" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.status == 2" color="blue">已发布</ATag>
            <ATag v-else>已下线</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="序号" data-index="position" :width="120" />
        <ATableColumn title="更新时间" data-index="updatedAt" :width="180" />

        <ATableColumn title="操作" :width="240">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/conference/banner/edit/${record.id}`">编辑</OpenRouterButton>
              <AButton status="danger" size="small" @click="changeStatus(1, record.id)" v-confirm v-if="record.status == 2">下线</AButton>
              <AButton type="primary" size="small" @click="changeStatus(2, record.id)" v-confirm v-else>发布</AButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.id)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
