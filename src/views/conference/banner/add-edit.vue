<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { bannerCategory } from '../constants';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref<Record<string, any>>({
  position: 0,
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id ? '/admin/meeting/v1/banner/update' : '/admin/meeting/v1/banner/add', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id: props.id,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/conference/banner/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/banner/detail', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      bannerType: data.bannerType,
      name: data.name,
      link: data.link,
      pic: data.pic,
      position: data.position,
      status: data.status,
    };
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <OpenLoadingProvider v-slot="{ loading: componentLoading }">
        <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit">
          <AFormItem label="位置" field="bannerType" :rules="[{ required: true, message: '此项必选' }]">
            <ASelect v-model="formData.bannerType" placeholder="请选择">
              <AOption v-for="(item, key) in bannerCategory" :value="key" :key="key">{{ item.text }}</AOption>
            </ASelect>
          </AFormItem>

          <AFormItem label="标题" field="name">
            <AInput v-model="formData.name" />
          </AFormItem>

          <AFormItem label="跳转链接" field="link" help="请输入 https:// 或者 http:// 开头的链接">
            <AInput v-model="formData.link" />
          </AFormItem>

          <AFormItem
            label="图片" field="pic"
            :rules="[{ required: true, message: '此项必传' }]"
            :help="bannerCategory[formData.bannerType] ? `图片尺寸: ${bannerCategory[formData.bannerType].picWidth}x${bannerCategory[formData.bannerType].picHeight} 像素` : ''"
          >
            <OpenUpload
              v-model="formData.pic"
              :image-aspect-ratio="bannerCategory[formData.bannerType] ? (bannerCategory[formData.bannerType].picWidth / bannerCategory[formData.bannerType].picHeight) : NaN"
            />
          </AFormItem>

          <AFormItem label="序号" field="position" :rules="[{ required: true, message: '此项必填' }]">
            <AInputNumber v-model="formData.position" />
          </AFormItem>

          <AFormItem label="状态" field="status">
            <ASwitch v-model="formData.status" :checked-value="2" :unchecked-value="1">
              <template #checked>
                已发布
              </template>
              <template #unchecked>
                已下线
              </template>
            </ASwitch>
          </AFormItem>

          <ADivider />

          <AFormItem>
            <ASpace>
              <OpenRouterButton to="/conference/banner/index">返回列表</OpenRouterButton>
              <AButton html-type="submit" type="primary" :loading="loading || componentLoading">提交</AButton>
            </ASpace>
          </AFormItem>
        </AForm>
      </OpenLoadingProvider>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
