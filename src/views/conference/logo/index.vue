<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { logoCategory } from '../constants';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success } = useNotification();

const form = ref({
  name: '',
  category: '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/logo/page', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      name: form.value.name,
      category: form.value.category,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/meeting/v1/logo/remove', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

onActivated(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="类型">
          <ASelect v-model="form.category">
            <AOption value="">全部</AOption>
            <AOption v-for="(item, key) in logoCategory" :value="key" :key="key">{{ item }}</AOption>
          </ASelect>
        </AFormItem>

        <AFormItem label="名称">
          <AInput v-model="form.name" allow-clear />
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/conference/logo/add" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="类型" :width="200">
          <template #cell="{ record }">
            {{ logoCategory[record.category] }}
          </template>
        </ATableColumn>

        <ATableColumn title="LOGO" header-cell-class="w-10%">
          <template #cell="{ record }">
            <OpenImage :src="record.pic" img-class="w-full h-100px object-contain" />
          </template>
        </ATableColumn>

        <ATableColumn title="名称" data-index="name" />
        <ATableColumn title="序号" data-index="position" :width="180" />
        <ATableColumn title="更新时间" data-index="updatedAt" :width="180" />

        <ATableColumn title="操作" :width="180">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/conference/logo/edit/${record.id}`">编辑</OpenRouterButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.id)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
