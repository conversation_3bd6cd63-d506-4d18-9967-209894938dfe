<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { logoCategory } from '../constants';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref<Record<string, any>>({
  position: 0,
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id ? '/admin/meeting/v1/logo/update' : '/admin/meeting/v1/logo/add', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id: props.id,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/conference/logo/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/logo/detail', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      category: data.category,
      name: data.name,
      pic: data.pic,
      position: data.position,
    };
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <OpenLoadingProvider v-slot="{ loading: componentLoading }">
        <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit">
          <AFormItem label="类型" field="category" :rules="[{ required: true, message: '此项必选' }]">
            <ASelect v-model="formData.category" placeholder="请选择">
              <AOption v-for="(item, key) in logoCategory" :value="key" :key="key">{{ item }}</AOption>
            </ASelect>
          </AFormItem>

          <AFormItem label="名称" field="name">
            <AInput v-model="formData.name" />
          </AFormItem>

          <AFormItem label="LOGO" field="pic" :rules="[{ required: true, message: '此项必传' }]" help="图片尺寸: 168x64 像素">
            <OpenUpload v-model="formData.pic" />
          </AFormItem>

          <AFormItem label="序号" field="position" :rules="[{ required: true, message: '此项必填' }]">
            <AInputNumber v-model="formData.position" />
          </AFormItem>

          <ADivider />

          <AFormItem>
            <ASpace>
              <OpenRouterButton to="/conference/logo/index">返回列表</OpenRouterButton>
              <AButton html-type="submit" type="primary" :loading="loading || componentLoading">提交</AButton>
            </ASpace>
          </AFormItem>
        </AForm>
      </OpenLoadingProvider>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
