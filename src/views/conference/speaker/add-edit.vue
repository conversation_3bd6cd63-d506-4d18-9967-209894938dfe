<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref<Record<string, any>>({
  position: 0,
  display: 1,
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id ? '/admin/meeting/v1/guest/update' : '/admin/meeting/v1/guest/add', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id: props.id,
      ...formData.value,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/conference/speaker/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/guest/detail', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      rank: data.rank,
      name: data.name,
      pic: data.pic,
      position: data.position,
      display: data.display,
    };
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <OpenLoadingProvider v-slot="{ loading: componentLoading }">
        <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit">
          <AFormItem label="名称" field="name" :rules="[{ required: true, message: '此项必填' }]">
            <AInput v-model="formData.name" />
          </AFormItem>

          <AFormItem label="头衔" field="rank" :rules="[{ required: true, message: '此项必填' }]">
            <AInput v-model="formData.rank" />
          </AFormItem>

          <AFormItem label="照片" field="pic" :rules="[{ required: true, message: '此项必传' }]" help="图片尺寸: 370x300 像素">
            <OpenUpload v-model="formData.pic" :image-aspect-ratio="370 / 300" />
          </AFormItem>

          <AFormItem label="序号" field="position" :rules="[{ required: true, message: '此项必填' }]">
            <AInputNumber v-model="formData.position" />
          </AFormItem>

          <AFormItem label="精选" field="display">
            <ASwitch v-model="formData.display" :checked-value="2" :unchecked-value="1">
              <template #checked>
                是
              </template>
              <template #unchecked>
                否
              </template>
            </ASwitch>
          </AFormItem>

          <ADivider />

          <AFormItem>
            <ASpace>
              <OpenRouterButton to="/conference/speaker/index">返回列表</OpenRouterButton>
              <AButton html-type="submit" type="primary" :loading="loading || componentLoading">提交</AButton>
            </ASpace>
          </AFormItem>
        </AForm>
      </OpenLoadingProvider>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
