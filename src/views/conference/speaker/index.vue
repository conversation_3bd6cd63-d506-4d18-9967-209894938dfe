<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success } = useNotification();

const form = ref({
  name: '',
  display: 0,
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/guest/page', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      name: form.value.name,
      display: form.value.display,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/meeting/v1/guest/remove', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const changeStatus = async (display: number, id: string) => {
  const { isSuccess } = await $fetch(`/admin/meeting/v1/guest/onOrOff`, {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id,
      display,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    search.value.refresh();
  }
};

onActivated(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="名称">
          <AInput v-model="form.name" allow-clear />
        </AFormItem>

        <AFormItem label="精选">
          <ASelect v-model="form.display">
            <AOption :value="0">全部</AOption>
            <AOption :value="2">是</AOption>
            <AOption :value="1">否</AOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/conference/speaker/add" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="名称" data-index="name" />
        <ATableColumn title="头衔" data-index="rank" />

        <ATableColumn title="照片" header-cell-class="w-80">
          <template #cell="{ record }">
            <OpenImage :src="record.pic" img-class="w-full h-100px object-contain object-left" />
          </template>
        </ATableColumn>

        <ATableColumn title="精选" :width="100">
          <template #cell="{ record }">
            <ATag v-if="record.display == 2" color="blue">是</ATag>
            <ATag v-else>否</ATag>
          </template>
        </ATableColumn>

        <ATableColumn title="序号" data-index="position" :width="120" />
        <ATableColumn title="更新时间" data-index="updatedAt" :width="180" />

        <ATableColumn title="操作" :width="260">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/conference/speaker/edit/${record.id}`">编辑</OpenRouterButton>
              <AButton size="small" @click="changeStatus(1, record.id)" v-confirm v-if="record.display == 2">取消精选</AButton>
              <AButton size="small" @click="changeStatus(2, record.id)" v-confirm v-else>精选</AButton>
              <AButton type="primary" status="danger" size="small" @click="remove(record.id)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
