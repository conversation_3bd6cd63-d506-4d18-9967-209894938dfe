<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from '@arco-design/web-vue/es/form';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { useRouter } from 'vue-router';
import { marked } from 'marked';
import MarkdownEditor from '@/components/markdown-editor/index.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
const formData = ref<Record<string, any>>({
  position: 0,
  time: [],
});
const formRef = ref<FormInstance>();
const router = useRouter();
const { success } = useNotification();

marked.use({
  gfm: true,
  breaks: true,
});

const submit = async () => {
  const res = await formRef.value?.validate();

  if (res) {
    return;
  }

  const { isSuccess } = await $fetch(props.id ? '/admin/meeting/v1/forum/update' : '/admin/meeting/v1/forum/add', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id: props.id,
      ...formData.value,
      beginTime: formData.value.time[0],
      endTime: formData.value.time[1],
      time: undefined,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('操作成功');
    router.push('/conference/forum/index');
  }
};

const getDetail = async () => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/forum/detail', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id: props.id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    formData.value = {
      name: data.name,
      description: data.description,
      holdDate: data.holdDate,
      time: [data.beginTime, data.endTime],
      dateDesc: data.dateDesc,
      conferenceName: data.conferenceName,
      conferencePlace: data.conferencePlace,
      publisherName: data.publisherName,
      publisherProfile: data.publisherProfile,
      publisherPic: data.publisherPic,
      position: data.position,
    };
  }
}

const markdownValidator = (value: string, callback: (error?: string) => void) => {
  const introText = (marked.parse(value || '') as string).replace(/<[^>]+>/g, ''); // 去除 HTML 标签
  if (introText.length > 500) {
    callback('不能超过 500 个字');
  }
  else {
    callback();
  }
}

const init = () => {
  if (props.id) {
    getDetail();
  }
}

init();
</script>

<template>
  <OpenPageSection>
    <OpenGeneralCard>
      <OpenLoadingProvider v-slot="{ loading: componentLoading }">
        <AForm ref="formRef" auto-label-width scroll-to-first-error :model="formData" @submit="submit">
          <AFormItem label="名称" field="name" :rules="[{ required: true, message: '此项必填' }]">
            <AInput v-model="formData.name" />
          </AFormItem>

          <AFormItem label="描述" field="description" :rules="[{ validator: markdownValidator }]" help="Markdown 格式">
            <MarkdownEditor :height="300" v-model="formData.description" break ref="editor" />
          </AFormItem>

          <AFormItem label="日期" field="holdDate" :rules="[{ required: true, message: '此项必填' }]">
            <ADatePicker v-model="formData.holdDate" />
          </AFormItem>

          <AFormItem label="时间" field="time" :rules="[{ required: true, message: '此项必填' }]">
            <ATimePicker type="time-range" format="HH:mm" v-model="formData.time" disable-confirm />
          </AFormItem>

          <AFormItem label="时间提示" field="dateDesc" help="一般填写上午或下午，也可以输入简短的其他文案">
            <AInput v-model="formData.dateDesc" />
          </AFormItem>

          <AFormItem label="会场名称" field="conferenceName" :rules="[{ required: true, message: '此项必填' }]">
            <AInput v-model="formData.conferenceName" :max-length="20" show-word-limit />
          </AFormItem>

          <AFormItem label="会场地点" field="conferencePlace">
            <AInput v-model="formData.conferencePlace" :max-length="20" show-word-limit />
          </AFormItem>

          <AFormItem label="出品人名称" field="publisherName">
            <AInput v-model="formData.publisherName" />
          </AFormItem>

          <AFormItem label="出品人职位" field="publisherProfile">
            <AInput v-model="formData.publisherProfile" />
          </AFormItem>

          <AFormItem label="出品人照片" field="publisherPic" help="图片尺寸: 240x240 像素">
            <OpenUpload v-model="formData.publisherPic" :image-aspect-ratio="240 / 240" />
          </AFormItem>

          <AFormItem label="序号" field="position" :rules="[{ required: true, message: '此项必填' }]">
            <AInputNumber v-model="formData.position" />
          </AFormItem>

          <ADivider />

          <AFormItem>
            <ASpace>
              <OpenRouterButton to="/conference/forum/index">返回列表</OpenRouterButton>
              <AButton html-type="submit" type="primary" :loading="loading || componentLoading">提交</AButton>
            </ASpace>
          </AFormItem>
        </AForm>
      </OpenLoadingProvider>
    </OpenGeneralCard>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
