<script lang="ts" setup>
import { ref, onActivated } from 'vue';
import { $fetch } from '@/utils/fetch';
import useNotification from '@/hooks/notification';
import { Modal, type ModalConfig } from '@arco-design/web-vue';

const listing = ref([]);
const pager = ref({});
const loading = ref(false);
const search = ref();
const { success } = useNotification();

const form = ref({
  name: '',
  conferenceName: '',
  category: '',
});

const refresh = async (page = 1) => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/forum/page', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      name: form.value.name,
      conferenceName: form.value.conferenceName,
      page,
      pageSize: 20,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    listing.value = data.list;

    pager.value = {
      current: page,
      total: data.totalCount,
    };
  }
}

const getScheduleTotal = async (id: string) => {
  const { isSuccess, data } = await $fetch('/admin/meeting/v1/agenda/page', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      forumId: id,
      page: 1,
      pageSize: 1,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    return data.totalCount as number;
  }

  return 0;
};

const remove = async (id: string) => {
  const { isSuccess } = await $fetch('/admin/meeting/v1/forum/remove', {
    data: {
      meetingCode: import.meta.env.VITE_ENABLED_CONFERENCE_CODE,
      id,
    },
    loadingStatus: loading,
  });

  if (isSuccess) {
    success('删除成功');
    search.value.refresh();
  }
};

const preRemove = async (id: string) => {
  const total = await getScheduleTotal(id);

  if (total < 1) {
    remove(id);
    return;
  }

  const modalConfig: ModalConfig = {
    title: '删除论坛',
    content: `当前论坛下有 ${total} 个议程，删除论坛，所有议程也会被删除，确认吗？`,
    simple: true,
    okText: '确认',
    onOk: async () => {
      remove(id);
    },
  };

  Modal.confirm(modalConfig);
};

onActivated(() => {
  search.value.refresh('reset');
});
</script>

<template>
  <OpenPageSection>
    <OpenSearchTable :pager="pager" :data="listing" :loading="loading" @search="refresh" ref="search">
      <template #header>
        <AFormItem label="论坛名称">
          <AInput v-model="form.name" allow-clear />
        </AFormItem>
        <AFormItem label="会场名称">
          <AInput v-model="form.conferenceName" allow-clear />
        </AFormItem>
      </template>

      <template #header-button>
        <OpenRouterButton to="/conference/forum/add" type="primary">新建</OpenRouterButton>
      </template>

      <template #columns>
        <ATableColumn title="论坛名称" data-index="name" />
        <ATableColumn title="日期" data-index="holdDate" />

        <ATableColumn title="时间">
          <template #cell="{ record }">
            {{ record.beginTime }} - {{ record.endTime }}
          </template>
        </ATableColumn>

        <ATableColumn title="会场名称" data-index="conferenceName" />
        <ATableColumn title="出品人" data-index="publisherName" />
        <ATableColumn title="序号" data-index="position" :width="120" />
        <ATableColumn title="更新时间" data-index="updatedAt" :width="180" />

        <ATableColumn title="操作" :width="260">
          <template #cell="{ record }">
            <div class="button-actions">
              <OpenRouterButton size="small" :to="`/conference/schedule/index/${record.id}`">查看议程</OpenRouterButton>
              <OpenRouterButton size="small" :to="`/conference/forum/edit/${record.id}`">编辑</OpenRouterButton>
              <AButton type="primary" status="danger" size="small" @click="preRemove(record.id)" v-confirm>删除</AButton>
            </div>
          </template>
        </ATableColumn>
      </template>
    </OpenSearchTable>
  </OpenPageSection>
</template>

<style lang="scss" scoped>
</style>
