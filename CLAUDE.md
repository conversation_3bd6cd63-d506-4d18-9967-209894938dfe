# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是开放原子开源基金会管理后台前端项目 - 一个用于管理开放原子开源基金会官网的 Vue 3 管理面板。项目基于 Vue 3 + TypeScript + Vite 构建，使用 Arco Design Vue 作为 UI 框架。

## 开发命令

### 安装依赖
```bash
pnpm i
```

### 开发环境
```bash
pnpm run dev         # 启动开发服务器
pnpm run dev:pm2     # 在开发机上使用 PM2 启动
```

### 构建和部署
```bash
pnpm run build              # 生产环境构建（包含类型检查）
pnpm run build:test         # 测试环境构建
pnpm run build:cdn          # 生产环境构建（使用 CDN）
pnpm run build:test:cdn     # 测试环境构建（使用 CDN）
pnpm run build:stats        # 构建并生成包分析报告
```

### 代码质量检查
```bash
pnpm run type-check         # TypeScript 类型检查
pnpm run lint               # 运行 ESLint 和 Stylelint
pnpm run lint:eslint        # 仅运行 ESLint
pnpm run lint:stylelint     # 仅运行 Stylelint
pnpm run code-check         # 运行类型检查和代码检查
```

## 架构说明

### 核心技术栈
- **Vue 3** 使用 Composition API
- **TypeScript** 提供类型安全
- **Vite** 构建工具（使用 rolldown-vite 提升构建速度）
- **Arco Design Vue** UI 组件库
- **Pinia** 状态管理
- **Vue Router** 路由管理
- **UnoCSS** 原子化 CSS 框架

### 关键目录结构
- `src/components/` - 可复用的 Vue 组件
- `src/views/` - 按功能模块组织的页面组件
- `src/router/` - 基于模块的路由配置
- `src/store/` - Pinia 状态管理（app、user、tab-bar）
- `src/utils/` - 工具函数（包括 API 客户端）
- `src/hooks/` - 组合式函数

### 模块结构
项目按功能模块组织：
- `welcome` - 仪表板/首页
- `journalism` - 新闻管理
- `information` - 信息管理
- `subscription` - 订阅管理
- `law` - 法律文档管理
- `events` - 活动管理
- `insight` - 洞察文章
- `partner` - 合作伙伴管理
- `about` - 关于页面管理
- `certification` - 认证管理
- `conference` - 会议管理
- `project` - 项目管理

每个模块通常包含：
- `index.vue` - 列表/表格视图
- `add-edit.vue` - 创建/编辑表单
- `use.ts` - 组合式逻辑（如适用）

### 状态管理
- **App Store** (`src/store/modules/app/`) - 全局应用状态
- **User Store** (`src/store/modules/user/`) - 用户认证和配置
- **Tab Bar Store** (`src/store/modules/tab-bar/`) - 标签页导航状态

### API 集成
- 自定义 `$fetch` 函数封装 axios (`src/utils/fetch.ts`)
- 自动注入 `X-OPENATOM-TOKEN` 认证头
- 错误处理包含通知和登出流程
- 支持文件上传（FormData）

### 开发环境配置
- API 代理：`/api/*` 路由代理到 `http://api.oafdev.cn`
- 支持本地配置文件 `vite.config.local.js`
- 不同构建模式的环境变量
- 生产构建支持 CDN

### 构建配置
- 高级分块策略优化加载性能
- 启用 source maps 用于调试
- 可通过 `USE_VISUALIZER=yes` 启用包分析器
- 自动图片优化
- 集成 ESLint 和 Stylelint 检查

## 特殊功能

### 自定义 Vite 插件
- `vite-plugin-upload-alioss` - 资源上传至阿里云 OSS
- `vite-plugin-replacer` - 构建输出字符串替换
- `vite-plugin-image-optimizer` - 图片压缩

### 编辑器集成
- TinyMCE 富文本编辑器（含自定义插件）
- Toast UI Editor 用于 Markdown 编辑
- Cropper.js 图片裁剪功能

### 测试和部署
- 测试环境：`http://alfred.openatom.oafdev.cn`
- 生产环境：`https://alfred.openatom.cn`
- 部署脚本在开发机 (`server.oafdev.cn`) 上可用

## 环境要求

- Node.js >= 22.15.0
- 使用 pnpm 进行包管理