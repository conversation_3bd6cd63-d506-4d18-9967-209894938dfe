# 开放原子开源基金会管理后台前端项目

* 线上地址: [https://alfred.openatom.cn](https://alfred.openatom.cn)
* 测试地址: [http://alfred.openatom.oafdev.cn](http://alfred.openatom.oafdev.cn)

## 开发环境

在本地或者 server.oafdev.cn 开发机上搭建开发环境。

NodeJS 版本要求 18.x

### 安装依赖

如果是本地开发请先执行安装 pnpm 的命令：

```bash
npm i -g pnpm
```

然后再安装依赖：

```bash
pnpm i
```

### 启动开发环境

#### 本机启动开发环境

```bash
pnpm run dev
```

#### 开发机启动开发环境

在项目根目录新建 `vite.config.local.js`

```javascript
import { mergeConfig } from 'vite';

export default function localViteConfig(defaultConfig) {
  return mergeConfig(defaultConfig, {
    server: {
      port: 自选端口号,
      strictPort: true,
    }
  });
};
```

`port` 请选择一个和其他人不一样的端口号，比如 20000 至 30000 之间随机选一个。

然后修改自己的 nginx 配置项，在你的家目录下的 `nginx.conf` 文件中追加：（如果没有这个文件自己新建一个）

```
server {
  server_name 你的姓名全拼.admin.openatom.oafdev.cn;

  access_log off;

  location / {
    proxy_pass http://127.0.0.1:上面选择的端口号;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header X-Forwarded-Port $server_port;
  }
}
```

然后找有权限的童鞋重启开发机的 nginx 即可。

启动开发服务器（只需执行一次）：

```bash
pnpm run dev:pm2
```

## 部署方式

部署命令需要登录 server.oafdev.cn 开发机执行，并需要加入 devops 用户组。

### 测试环境

执行：

```bash
sudo testing_openatom_admin_www
```

### 生产环境

构建并部署生产环境分支最新版代码，并自动打 tag，执行：

```bash
sudo build_and_deploy_openatom_admin_www
```

如果需要部署某个版本的代码，请执行：

```bash
sudo deploy_openatom_admin_www [标签名]
```

其中 `标签名` 是 git 中的以 release- 开头的 tag 名称，如果不写 `标签名` 则表示部署最新代码。

例如：

```bash
sudo deploy_openatom_admin_www release-xxx
```

## 项目说明

项目整体基于 Arco Design Vue 开发，具体请参考 https://arco.design/vue/docs/start
